hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@antfu/utils@0.7.10':
    '@antfu/utils': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.27.3':
    '@babel/compat-data': private
  '@babel/core@7.27.3':
    '@babel/core': private
  '@babel/generator@7.27.3':
    '@babel/generator': private
  '@babel/helper-annotate-as-pure@7.27.3':
    '@babel/helper-annotate-as-pure': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.27.3)':
    '@babel/helper-create-class-features-plugin': private
  '@babel/helper-member-expression-to-functions@7.27.1':
    '@babel/helper-member-expression-to-functions': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.27.3)':
    '@babel/helper-module-transforms': private
  '@babel/helper-optimise-call-expression@7.27.1':
    '@babel/helper-optimise-call-expression': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-replace-supers@7.27.1(@babel/core@7.27.3)':
    '@babel/helper-replace-supers': private
  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    '@babel/helper-skip-transparent-expression-wrappers': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.3':
    '@babel/helpers': private
  '@babel/parser@7.27.3':
    '@babel/parser': private
  '@babel/plugin-proposal-decorators@7.27.1(@babel/core@7.27.3)':
    '@babel/plugin-proposal-decorators': private
  '@babel/plugin-syntax-decorators@7.27.1(@babel/core@7.27.3)':
    '@babel/plugin-syntax-decorators': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.27.3)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.27.3)':
    '@babel/plugin-syntax-import-meta': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.27.3)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.27.3)':
    '@babel/plugin-syntax-typescript': private
  '@babel/plugin-transform-typescript@7.27.1(@babel/core@7.27.3)':
    '@babel/plugin-transform-typescript': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.27.3':
    '@babel/traverse': private
  '@babel/types@7.27.3':
    '@babel/types': private
  '@esbuild/aix-ppc64@0.25.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.5':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.5':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.5':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.5':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.5':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.5':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.5':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.5':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.5':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.5':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.5':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.5':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.5':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.5':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.5':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.5':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.5':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.5':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.5':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.5':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.5':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.5':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.5':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.5':
    '@esbuild/win32-x64': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@polka/url@1.0.0-next.29':
    '@polka/url': private
  '@rollup/pluginutils@5.1.4(rollup@4.41.1)':
    '@rollup/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.41.1':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.41.1':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.41.1':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.41.1':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.41.1':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.41.1':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.41.1':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.41.1':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.41.1':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.41.1':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.41.1':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.41.1':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.41.1':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.41.1':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.41.1':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.41.1':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.41.1':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.41.1':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.41.1':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.41.1':
    '@rollup/rollup-win32-x64-msvc': private
  '@sec-ant/readable-stream@0.4.1':
    '@sec-ant/readable-stream': private
  '@sindresorhus/merge-streams@4.0.0':
    '@sindresorhus/merge-streams': private
  '@types/estree@1.0.7':
    '@types/estree': private
  '@types/web-bluetooth@0.0.21':
    '@types/web-bluetooth': private
  '@vue/babel-helper-vue-transform-on@1.4.0':
    '@vue/babel-helper-vue-transform-on': private
  '@vue/babel-plugin-jsx@1.4.0(@babel/core@7.27.3)':
    '@vue/babel-plugin-jsx': private
  '@vue/babel-plugin-resolve-type@1.4.0(@babel/core@7.27.3)':
    '@vue/babel-plugin-resolve-type': private
  '@vue/compiler-core@3.5.16':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.16':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.5.16':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.5.16':
    '@vue/compiler-ssr': private
  '@vue/devtools-api@7.7.6':
    '@vue/devtools-api': private
  '@vue/devtools-core@7.7.6(vite@6.3.5)(vue@3.5.16)':
    '@vue/devtools-core': private
  '@vue/devtools-kit@7.7.6':
    '@vue/devtools-kit': private
  '@vue/devtools-shared@7.7.6':
    '@vue/devtools-shared': private
  '@vue/reactivity@3.5.16':
    '@vue/reactivity': private
  '@vue/runtime-core@3.5.16':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.5.16':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.5.16(vue@3.5.16)':
    '@vue/server-renderer': private
  '@vue/shared@3.5.16':
    '@vue/shared': private
  '@vueuse/metadata@13.3.0':
    '@vueuse/metadata': private
  '@vueuse/shared@13.3.0(vue@3.5.16)':
    '@vueuse/shared': private
  ansi-regex@6.1.0:
    ansi-regex: private
  base64-js@1.5.1:
    base64-js: private
  birpc@2.3.0:
    birpc: private
  bl@5.1.0:
    bl: private
  browserslist@4.25.0:
    browserslist: private
  buffer@6.0.3:
    buffer: private
  bundle-name@4.1.0:
    bundle-name: private
  caniuse-lite@1.0.30001720:
    caniuse-lite: private
  chalk@5.2.0:
    chalk: private
  cli-cursor@4.0.0:
    cli-cursor: private
  cli-spinners@2.9.2:
    cli-spinners: private
  clone@1.0.4:
    clone: private
  commander@10.0.1:
    commander: private
  convert-source-map@2.0.0:
    convert-source-map: private
  copy-anything@3.0.5:
    copy-anything: private
  cross-spawn@7.0.6:
    cross-spawn: private
  csstype@3.1.3:
    csstype: private
  data-uri-to-buffer@4.0.1:
    data-uri-to-buffer: private
  debug@4.4.1:
    debug: private
  default-browser-id@5.0.0:
    default-browser-id: private
  default-browser@5.2.1:
    default-browser: private
  defaults@1.0.4:
    defaults: private
  define-lazy-prop@3.0.0:
    define-lazy-prop: private
  electron-to-chromium@1.5.161:
    electron-to-chromium: private
  entities@4.5.0:
    entities: private
  error-stack-parser-es@0.1.5:
    error-stack-parser-es: private
  esbuild@0.25.5:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  estree-walker@2.0.2:
    estree-walker: private
  execa@7.2.0:
    execa: private
  fdir@6.4.5(picomatch@4.0.2):
    fdir: private
  fetch-blob@3.2.0:
    fetch-blob: private
  figures@6.1.0:
    figures: private
  formdata-polyfill@4.0.10:
    formdata-polyfill: private
  fs-extra@11.3.0:
    fs-extra: private
  fsevents@2.3.3:
    fsevents: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-stream@6.0.1:
    get-stream: private
  globals@11.12.0:
    globals: private
  graceful-fs@4.2.11:
    graceful-fs: private
  hookable@5.5.3:
    hookable: private
  human-signals@4.3.1:
    human-signals: private
  ieee754@1.2.1:
    ieee754: private
  inherits@2.0.4:
    inherits: private
  is-docker@3.0.0:
    is-docker: private
  is-inside-container@1.0.0:
    is-inside-container: private
  is-interactive@2.0.0:
    is-interactive: private
  is-plain-obj@4.1.0:
    is-plain-obj: private
  is-stream@3.0.0:
    is-stream: private
  is-unicode-supported@1.3.0:
    is-unicode-supported: private
  is-what@4.1.16:
    is-what: private
  is-wsl@3.1.0:
    is-wsl: private
  isexe@2.0.0:
    isexe: private
  js-tokens@4.0.0:
    js-tokens: private
  jsesc@3.1.0:
    jsesc: private
  json5@2.2.3:
    json5: private
  jsonfile@6.1.0:
    jsonfile: private
  kleur@3.0.3:
    kleur: private
  kolorist@1.8.0:
    kolorist: private
  log-symbols@5.1.0:
    log-symbols: private
  lru-cache@5.1.1:
    lru-cache: private
  magic-string@0.30.17:
    magic-string: private
  merge-stream@2.0.0:
    merge-stream: private
  mimic-fn@4.0.0:
    mimic-fn: private
  mitt@3.0.1:
    mitt: private
  mrmime@2.0.1:
    mrmime: private
  ms@2.1.3:
    ms: private
  nanoid@5.1.5:
    nanoid: private
  node-domexception@1.0.0:
    node-domexception: private
  node-fetch@3.3.2:
    node-fetch: private
  node-releases@2.0.19:
    node-releases: private
  npm-run-path@5.3.0:
    npm-run-path: private
  onetime@6.0.0:
    onetime: private
  open@10.1.2:
    open: private
  ora@6.3.1:
    ora: private
  parse-ms@4.0.0:
    parse-ms: private
  path-key@3.1.1:
    path-key: private
  pathe@2.0.3:
    pathe: private
  perfect-debounce@1.0.0:
    perfect-debounce: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  postcss@8.5.4:
    postcss: private
  pretty-ms@9.2.0:
    pretty-ms: private
  prompts@2.4.2:
    prompts: private
  readable-stream@3.6.2:
    readable-stream: private
  restore-cursor@4.0.0:
    restore-cursor: private
  rfdc@1.4.1:
    rfdc: private
  rollup@4.41.1:
    rollup: private
  run-applescript@7.0.0:
    run-applescript: private
  safe-buffer@5.2.1:
    safe-buffer: private
  semver@6.3.1:
    semver: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  signal-exit@3.0.7:
    signal-exit: private
  sirv@3.0.1:
    sirv: private
  sisteransi@1.0.5:
    sisteransi: private
  source-map-js@1.2.1:
    source-map-js: private
  speakingurl@14.0.1:
    speakingurl: private
  stdin-discarder@0.1.0:
    stdin-discarder: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@7.1.0:
    strip-ansi: private
  strip-final-newline@3.0.0:
    strip-final-newline: private
  superjson@2.2.2:
    superjson: private
  tinyglobby@0.2.14:
    tinyglobby: private
  totalist@3.0.1:
    totalist: private
  unicorn-magic@0.3.0:
    unicorn-magic: private
  universalify@2.0.1:
    universalify: private
  update-browserslist-db@1.1.3(browserslist@4.25.0):
    update-browserslist-db: private
  util-deprecate@1.0.2:
    util-deprecate: private
  vite-hot-client@2.0.4(vite@6.3.5):
    vite-hot-client: private
  vite-plugin-inspect@0.8.9(rollup@4.41.1)(vite@6.3.5):
    vite-plugin-inspect: private
  vite-plugin-vue-inspector@5.3.1(vite@6.3.5):
    vite-plugin-vue-inspector: private
  wcwidth@1.0.1:
    wcwidth: private
  web-streams-polyfill@3.3.3:
    web-streams-polyfill: private
  which@2.0.2:
    which: private
  yallist@3.1.1:
    yallist: private
  yoctocolors@2.1.1:
    yoctocolors: private
  zod@3.25.41:
    zod: private
ignoredBuilds:
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.0
pendingBuilds: []
prunedAt: Fri, 30 May 2025 03:17:58 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/linux-x64@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.25.5'
  - '@esbuild/win32-x64@0.25.5'
  - '@rollup/rollup-android-arm-eabi@4.41.1'
  - '@rollup/rollup-android-arm64@4.41.1'
  - '@rollup/rollup-darwin-x64@4.41.1'
  - '@rollup/rollup-freebsd-arm64@4.41.1'
  - '@rollup/rollup-freebsd-x64@4.41.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.41.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.41.1'
  - '@rollup/rollup-linux-arm64-gnu@4.41.1'
  - '@rollup/rollup-linux-arm64-musl@4.41.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.41.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.41.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.41.1'
  - '@rollup/rollup-linux-riscv64-musl@4.41.1'
  - '@rollup/rollup-linux-s390x-gnu@4.41.1'
  - '@rollup/rollup-linux-x64-gnu@4.41.1'
  - '@rollup/rollup-linux-x64-musl@4.41.1'
  - '@rollup/rollup-win32-arm64-msvc@4.41.1'
  - '@rollup/rollup-win32-ia32-msvc@4.41.1'
  - '@rollup/rollup-win32-x64-msvc@4.41.1'
storeDir: /Volumes/Data/.pnpm-store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
