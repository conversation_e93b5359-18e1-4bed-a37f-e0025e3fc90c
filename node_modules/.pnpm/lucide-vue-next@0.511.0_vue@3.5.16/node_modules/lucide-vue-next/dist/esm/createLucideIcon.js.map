{"version": 3, "file": "createLucideIcon.js", "sources": ["../../src/createLucideIcon.ts"], "sourcesContent": ["import { h } from 'vue';\nimport type { FunctionalComponent } from 'vue';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n// Create interface extending SVGAttributes\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {FunctionalComponent} LucideIcon\n */\nconst createLucideIcon =\n  (iconName: string, iconNode: IconNode): FunctionalComponent<LucideProps> =>\n  (props, { slots }) =>\n    h(\n      Icon,\n      {\n        ...props,\n        iconNode,\n        name: iconName,\n      },\n      slots,\n    );\n\nexport default createLucideIcon;\n"], "names": [], "mappings": ";;;;;;;;;;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACJ,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACnB,CAAC,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAE,OACR,KAAA,CAAA;AAAA,CAAA,CACE,CAAA,CAAA,CAAA,CAAA;AAAA,CACA,CAAA;AAAA,CAAA,CAAA,CAAA,CACE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CAAA,CAAA,CAAA,CACH,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,CACR,CAAA,CAAA;AAAA,CAAA,CACA,CAAA,CAAA,CAAA,CAAA;AACF,CAAA;;"}