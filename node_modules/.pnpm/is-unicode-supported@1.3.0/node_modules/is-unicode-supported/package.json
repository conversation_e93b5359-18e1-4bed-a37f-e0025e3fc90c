{"name": "is-unicode-supported", "version": "1.3.0", "description": "Detect whether the terminal supports Unicode", "license": "MIT", "repository": "sindresorhus/is-unicode-supported", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["terminal", "unicode", "detect", "utf8", "console", "shell", "support", "supports", "supported", "check", "detection"], "devDependencies": {"ava": "^4.0.1", "tsd": "^0.19.1", "xo": "^0.47.0"}}