(function(E,g,n){"use strict";function Ce(e,t,o){let l;n.isRef(o)?l={evaluating:o}:l=o||{};const{lazy:r=!1,flush:s="pre",evaluating:i=void 0,shallow:a=!0,onError:u=g.noop}=l,f=n.shallowRef(!r),c=a?n.shallowRef(t):n.ref(t);let d=0;return n.watchEffect(async h=>{if(!f.value)return;d++;const m=d;let v=!1;i&&Promise.resolve().then(()=>{i.value=!0});try{const S=await e(p=>{h(()=>{i&&(i.value=!1),v||p()})});m===d&&(c.value=S)}catch(S){u(S)}finally{i&&m===d&&(i.value=!1),v=!0}},{flush:s}),r?n.computed(()=>(f.value=!0,c.value)):c}function kt(e,t,o,l){let r=n.inject(e);return o&&(r=n.inject(e,o)),l&&(r=n.inject(e,o,l)),typeof t=="function"?n.computed(s=>t(r,s)):n.computed({get:s=>t.get(r,s),set:t.set})}function _t(e={}){const{inheritAttrs:t=!0}=e,o=n.shallowRef(),l=n.defineComponent({setup(s,{slots:i}){return()=>{o.value=i.default}}}),r=n.defineComponent({inheritAttrs:t,props:e.props,setup(s,{attrs:i,slots:a}){return()=>{var u;if(!o.value&&process.env.NODE_ENV!=="production")throw new Error("[VueUse] Failed to find the definition of reusable template");const f=(u=o.value)==null?void 0:u.call(o,{...e.props==null?Vt(i):s,$slots:a});return t&&f?.length===1?f[0]:f}}});return g.makeDestructurable({define:l,reuse:r},[l,r])}function Vt(e){const t={};for(const o in e)t[g.camelize(o)]=e[o];return t}function Ft(e={}){let t=0;const o=n.ref([]);function l(...i){const a=n.shallowReactive({key:t++,args:i,promise:void 0,resolve:()=>{},reject:()=>{},isResolving:!1,options:e});return o.value.push(a),a.promise=new Promise((u,f)=>{a.resolve=c=>(a.isResolving=!0,u(c)),a.reject=f}).finally(()=>{a.promise=void 0;const u=o.value.indexOf(a);u!==-1&&o.value.splice(u,1)}),a.promise}function r(...i){return e.singleton&&o.value.length>0?o.value[0].promise:l(...i)}const s=n.defineComponent((i,{slots:a})=>{const u=()=>o.value.map(f=>{var c;return n.h(n.Fragment,{key:f.key},(c=a.default)==null?void 0:c.call(a,f))});return e.transition?()=>n.h(n.TransitionGroup,e.transition,u):u});return s.start=r,s}function Pt(e){return function(...t){return e.apply(this,t.map(o=>n.toValue(o)))}}const M=g.isClient?window:void 0,j=g.isClient?window.document:void 0,z=g.isClient?window.navigator:void 0,Ct=g.isClient?window.location:void 0;function x(e){var t;const o=n.toValue(e);return(t=o?.$el)!=null?t:o}function O(...e){const t=[],o=()=>{t.forEach(a=>a()),t.length=0},l=(a,u,f,c)=>(a.addEventListener(u,f,c),()=>a.removeEventListener(u,f,c)),r=n.computed(()=>{const a=g.toArray(n.toValue(e[0])).filter(u=>u!=null);return a.every(u=>typeof u!="string")?a:void 0}),s=g.watchImmediate(()=>{var a,u;return[(u=(a=r.value)==null?void 0:a.map(f=>x(f)))!=null?u:[M].filter(f=>f!=null),g.toArray(n.toValue(r.value?e[1]:e[0])),g.toArray(n.unref(r.value?e[2]:e[1])),n.toValue(r.value?e[3]:e[2])]},([a,u,f,c])=>{if(o(),!a?.length||!u?.length||!f?.length)return;const d=g.isObject(c)?{...c}:c;t.push(...a.flatMap(h=>u.flatMap(m=>f.map(v=>l(h,m,v,d)))))},{flush:"post"}),i=()=>{s(),o()};return g.tryOnScopeDispose(o),i}let De=!1;function Dt(e,t,o={}){const{window:l=M,ignore:r=[],capture:s=!0,detectIframe:i=!1,controls:a=!1}=o;if(!l)return a?{stop:g.noop,cancel:g.noop,trigger:g.noop}:g.noop;if(g.isIOS&&!De){De=!0;const p={passive:!0};Array.from(l.document.body.children).forEach(w=>w.addEventListener("click",g.noop,p)),l.document.documentElement.addEventListener("click",g.noop,p)}let u=!0;const f=p=>n.toValue(r).some(w=>{if(typeof w=="string")return Array.from(l.document.querySelectorAll(w)).some(y=>y===p.target||p.composedPath().includes(y));{const y=x(w);return y&&(p.target===y||p.composedPath().includes(y))}});function c(p){const w=n.toValue(p);return w&&w.$.subTree.shapeFlag===16}function d(p,w){const y=n.toValue(p),b=y.$.subTree&&y.$.subTree.children;return b==null||!Array.isArray(b)?!1:b.some(T=>T.el===w.target||w.composedPath().includes(T.el))}const h=p=>{const w=x(e);if(p.target!=null&&!(!(w instanceof Element)&&c(e)&&d(e,p))&&!(!w||w===p.target||p.composedPath().includes(w))){if("detail"in p&&p.detail===0&&(u=!f(p)),!u){u=!0;return}t(p)}};let m=!1;const v=[O(l,"click",p=>{m||(m=!0,setTimeout(()=>{m=!1},0),h(p))},{passive:!0,capture:s}),O(l,"pointerdown",p=>{const w=x(e);u=!f(p)&&!!(w&&!p.composedPath().includes(w))},{passive:!0}),i&&O(l,"blur",p=>{setTimeout(()=>{var w;const y=x(e);((w=l.document.activeElement)==null?void 0:w.tagName)==="IFRAME"&&!y?.contains(l.document.activeElement)&&t(p)},0)},{passive:!0})].filter(Boolean),S=()=>v.forEach(p=>p());return a?{stop:S,cancel:()=>{u=!1},trigger:p=>{u=!0,h(p),u=!1}}:S}function Ae(){const e=n.shallowRef(!1),t=n.getCurrentInstance();return t&&n.onMounted(()=>{e.value=!0},t),e}function W(e){const t=Ae();return n.computed(()=>(t.value,!!e()))}function Q(e,t,o={}){const{window:l=M,...r}=o;let s;const i=W(()=>l&&"MutationObserver"in l),a=()=>{s&&(s.disconnect(),s=void 0)},u=n.computed(()=>{const h=n.toValue(e),m=g.toArray(h).map(x).filter(g.notNullish);return new Set(m)}),f=n.watch(()=>u.value,h=>{a(),i.value&&h.size&&(s=new MutationObserver(t),h.forEach(m=>s.observe(m,r)))},{immediate:!0,flush:"post"}),c=()=>s?.takeRecords(),d=()=>{f(),a()};return g.tryOnScopeDispose(d),{isSupported:i,stop:d,takeRecords:c}}function we(e,t,o={}){const{window:l=M,document:r=l?.document,flush:s="sync"}=o;if(!l||!r)return g.noop;let i;const a=c=>{i?.(),i=c},u=n.watchEffect(()=>{const c=x(e);if(c){const{stop:d}=Q(r,h=>{h.map(v=>[...v.removedNodes]).flat().some(v=>v===c||v.contains(c))&&t(h)},{window:l,childList:!0,subtree:!0});a(d)}},{flush:s}),f=()=>{u(),a()};return g.tryOnScopeDispose(f),f}function At(e){return typeof e=="function"?e:typeof e=="string"?t=>t.key===e:Array.isArray(e)?t=>e.includes(t.key):()=>!0}function ie(...e){let t,o,l={};e.length===3?(t=e[0],o=e[1],l=e[2]):e.length===2?typeof e[1]=="object"?(t=!0,o=e[0],l=e[1]):(t=e[0],o=e[1]):(t=!0,o=e[0]);const{target:r=M,eventName:s="keydown",passive:i=!1,dedupe:a=!1}=l,u=At(t);return O(r,s,c=>{c.repeat&&n.toValue(a)||u(c)&&o(c)},i)}function Mt(e,t,o={}){return ie(e,t,{...o,eventName:"keydown"})}function It(e,t,o={}){return ie(e,t,{...o,eventName:"keypress"})}function Lt(e,t,o={}){return ie(e,t,{...o,eventName:"keyup"})}const Nt=500,xt=10;function Wt(e,t,o){var l,r;const s=n.computed(()=>x(e));let i,a,u,f=!1;function c(){i&&(clearTimeout(i),i=void 0),a=void 0,u=void 0,f=!1}function d(w){var y,b,T;const[k,C,R]=[u,a,f];if(c(),!o?.onMouseUp||!C||!k||(y=o?.modifiers)!=null&&y.self&&w.target!==s.value)return;(b=o?.modifiers)!=null&&b.prevent&&w.preventDefault(),(T=o?.modifiers)!=null&&T.stop&&w.stopPropagation();const V=w.x-C.x,P=w.y-C.y,I=Math.sqrt(V*V+P*P);o.onMouseUp(w.timeStamp-k,I,R)}function h(w){var y,b,T,k;(y=o?.modifiers)!=null&&y.self&&w.target!==s.value||(c(),(b=o?.modifiers)!=null&&b.prevent&&w.preventDefault(),(T=o?.modifiers)!=null&&T.stop&&w.stopPropagation(),a={x:w.x,y:w.y},u=w.timeStamp,i=setTimeout(()=>{f=!0,t(w)},(k=o?.delay)!=null?k:Nt))}function m(w){var y,b,T,k;if((y=o?.modifiers)!=null&&y.self&&w.target!==s.value||!a||o?.distanceThreshold===!1)return;(b=o?.modifiers)!=null&&b.prevent&&w.preventDefault(),(T=o?.modifiers)!=null&&T.stop&&w.stopPropagation();const C=w.x-a.x,R=w.y-a.y;Math.sqrt(C*C+R*R)>=((k=o?.distanceThreshold)!=null?k:xt)&&c()}const v={capture:(l=o?.modifiers)==null?void 0:l.capture,once:(r=o?.modifiers)==null?void 0:r.once},S=[O(s,"pointerdown",h,v),O(s,"pointermove",m,v),O(s,["pointerup","pointerleave"],d,v)];return()=>S.forEach(w=>w())}function Ht(){const{activeElement:e,body:t}=document;if(!e||e===t)return!1;switch(e.tagName){case"INPUT":case"TEXTAREA":return!0}return e.hasAttribute("contenteditable")}function Ut({keyCode:e,metaKey:t,ctrlKey:o,altKey:l}){return t||o||l?!1:e>=48&&e<=57||e>=96&&e<=105||e>=65&&e<=90}function $t(e,t={}){const{document:o=j}=t;o&&O(o,"keydown",r=>{!Ht()&&Ut(r)&&e(r)},{passive:!0})}function Bt(e,t=null){const o=n.getCurrentInstance();let l=()=>{};const r=n.customRef((s,i)=>(l=i,{get(){var a,u;return s(),(u=(a=o?.proxy)==null?void 0:a.$refs[e])!=null?u:t},set(){}}));return g.tryOnMounted(l),n.onUpdated(l),r}function Me(e={}){var t;const{window:o=M,deep:l=!0,triggerOnRemoval:r=!1}=e,s=(t=e.document)!=null?t:o?.document,i=()=>{var f;let c=s?.activeElement;if(l)for(;c?.shadowRoot;)c=(f=c?.shadowRoot)==null?void 0:f.activeElement;return c},a=n.shallowRef(),u=()=>{a.value=i()};if(o){const f={capture:!0,passive:!0};O(o,"blur",c=>{c.relatedTarget===null&&u()},f),O(o,"focus",u,f)}return r&&we(a,u,{document:s}),u(),a}function Y(e,t={}){const{immediate:o=!0,fpsLimit:l=void 0,window:r=M,once:s=!1}=t,i=n.shallowRef(!1),a=n.computed(()=>l?1e3/n.toValue(l):null);let u=0,f=null;function c(m){if(!i.value||!r)return;u||(u=m);const v=m-u;if(a.value&&v<a.value){f=r.requestAnimationFrame(c);return}if(u=m,e({delta:v,timestamp:m}),s){i.value=!1,f=null;return}f=r.requestAnimationFrame(c)}function d(){!i.value&&r&&(i.value=!0,u=0,f=r.requestAnimationFrame(c))}function h(){i.value=!1,f!=null&&r&&(r.cancelAnimationFrame(f),f=null)}return o&&d(),g.tryOnScopeDispose(h),{isActive:n.readonly(i),pause:h,resume:d}}function jt(e,t,o){let l,r;g.isObject(o)?(l=o,r=g.objectOmit(o,["window","immediate","commitStyles","persist","onReady","onError"])):(l={duration:o},r=o);const{window:s=M,immediate:i=!0,commitStyles:a,persist:u,playbackRate:f=1,onReady:c,onError:d=A=>{console.error(A)}}=l,h=W(()=>s&&HTMLElement&&"animate"in HTMLElement.prototype),m=n.shallowRef(void 0),v=n.shallowReactive({startTime:null,currentTime:null,timeline:null,playbackRate:f,pending:!1,playState:i?"idle":"paused",replaceState:"active"}),S=n.computed(()=>v.pending),p=n.computed(()=>v.playState),w=n.computed(()=>v.replaceState),y=n.computed({get(){return v.startTime},set(A){v.startTime=A,m.value&&(m.value.startTime=A)}}),b=n.computed({get(){return v.currentTime},set(A){v.currentTime=A,m.value&&(m.value.currentTime=A,N())}}),T=n.computed({get(){return v.timeline},set(A){v.timeline=A,m.value&&(m.value.timeline=A)}}),k=n.computed({get(){return v.playbackRate},set(A){v.playbackRate=A,m.value&&(m.value.playbackRate=A)}}),C=()=>{if(m.value)try{m.value.play(),N()}catch(A){H(),d(A)}else L()},R=()=>{var A;try{(A=m.value)==null||A.pause(),H()}catch(U){d(U)}},V=()=>{var A;m.value||L();try{(A=m.value)==null||A.reverse(),N()}catch(U){H(),d(U)}},P=()=>{var A;try{(A=m.value)==null||A.finish(),H()}catch(U){d(U)}},I=()=>{var A;try{(A=m.value)==null||A.cancel(),H()}catch(U){d(U)}};n.watch(()=>x(e),A=>{A?L():m.value=void 0}),n.watch(()=>t,A=>{if(m.value){L();const U=x(e);U&&(m.value.effect=new KeyframeEffect(U,n.toValue(A),r))}},{deep:!0}),g.tryOnMounted(()=>L(!0),!1),g.tryOnScopeDispose(I);function L(A){const U=x(e);!h.value||!U||(m.value||(m.value=U.animate(n.toValue(t),r)),u&&m.value.persist(),f!==1&&(m.value.playbackRate=f),A&&!i?m.value.pause():N(),c?.(m.value))}const _={passive:!0};O(m,["cancel","finish","remove"],H,_),O(m,"finish",()=>{var A;a&&((A=m.value)==null||A.commitStyles())},_);const{resume:D,pause:F}=Y(()=>{m.value&&(v.pending=m.value.pending,v.playState=m.value.playState,v.replaceState=m.value.replaceState,v.startTime=m.value.startTime,v.currentTime=m.value.currentTime,v.timeline=m.value.timeline,v.playbackRate=m.value.playbackRate)},{immediate:!1});function N(){h.value&&D()}function H(){h.value&&s&&s.requestAnimationFrame(F)}return{isSupported:h,animate:m,play:C,pause:R,reverse:V,finish:P,cancel:I,pending:S,playState:p,replaceState:w,startTime:y,currentTime:b,timeline:T,playbackRate:k}}function zt(e,t){const{interrupt:o=!0,onError:l=g.noop,onFinished:r=g.noop,signal:s}=t||{},i={aborted:"aborted",fulfilled:"fulfilled",pending:"pending",rejected:"rejected"},a=Array.from(Array.from({length:e.length}),()=>({state:i.pending,data:null})),u=n.reactive(a),f=n.shallowRef(-1);if(!e||e.length===0)return r(),{activeIndex:f,result:u};function c(d,h){f.value++,u[f.value].data=h,u[f.value].state=d}return e.reduce((d,h)=>d.then(m=>{var v;if(s?.aborted){c(i.aborted,new Error("aborted"));return}if(((v=u[f.value])==null?void 0:v.state)===i.rejected&&o){r();return}const S=h(m).then(p=>(c(i.fulfilled,p),f.value===e.length-1&&r(),p));return s?Promise.race([S,qt(s)]):S}).catch(m=>s?.aborted?(c(i.aborted,m),m):(c(i.rejected,m),l(),m)),Promise.resolve()),{activeIndex:f,result:u}}function qt(e){return new Promise((t,o)=>{const l=new Error("aborted");e.aborted?o(l):e.addEventListener("abort",()=>o(l),{once:!0})})}function Ie(e,t,o){const{immediate:l=!0,delay:r=0,onError:s=g.noop,onSuccess:i=g.noop,resetOnExecute:a=!0,shallow:u=!0,throwError:f}=o??{},c=u?n.shallowRef(t):n.ref(t),d=n.shallowRef(!1),h=n.shallowRef(!1),m=n.shallowRef(void 0);async function v(w=0,...y){a&&(c.value=t),m.value=void 0,d.value=!1,h.value=!0,w>0&&await g.promiseTimeout(w);const b=typeof e=="function"?e(...y):e;try{const T=await b;c.value=T,d.value=!0,i(T)}catch(T){if(m.value=T,s(T),f)throw T}finally{h.value=!1}return c.value}l&&v(r);const S={state:c,isReady:d,isLoading:h,error:m,execute:v};function p(){return new Promise((w,y)=>{g.until(h).toBe(!1).then(()=>w(S)).catch(y)})}return{...S,then(w,y){return p().then(w,y)}}}const te={array:e=>JSON.stringify(e),object:e=>JSON.stringify(e),set:e=>JSON.stringify(Array.from(e)),map:e=>JSON.stringify(Object.fromEntries(e)),null:()=>""};function Gt(e){return e?e instanceof Map?te.map:e instanceof Set?te.set:Array.isArray(e)?te.array:te.object:te.null}function Yt(e,t){const o=n.shallowRef(""),l=n.shallowRef();function r(){if(g.isClient)return l.value=new Promise((s,i)=>{try{const a=n.toValue(e);if(a==null)s("");else if(typeof a=="string")s(ge(new Blob([a],{type:"text/plain"})));else if(a instanceof Blob)s(ge(a));else if(a instanceof ArrayBuffer)s(window.btoa(String.fromCharCode(...new Uint8Array(a))));else if(a instanceof HTMLCanvasElement)s(a.toDataURL(t?.type,t?.quality));else if(a instanceof HTMLImageElement){const u=a.cloneNode(!1);u.crossOrigin="Anonymous",Xt(u).then(()=>{const f=document.createElement("canvas"),c=f.getContext("2d");f.width=u.width,f.height=u.height,c.drawImage(u,0,0,f.width,f.height),s(f.toDataURL(t?.type,t?.quality))}).catch(i)}else if(typeof a=="object"){const f=(t?.serializer||Gt(a))(a);return s(ge(new Blob([f],{type:"application/json"})))}else i(new Error("target is unsupported types"))}catch(a){i(a)}}),l.value.then(s=>{o.value=t?.dataUrl===!1?s.replace(/^data:.*?;base64,/,""):s}),l.value}return n.isRef(e)||typeof e=="function"?n.watch(e,r,{immediate:!0}):r(),{base64:o,promise:l,execute:r}}function Xt(e){return new Promise((t,o)=>{e.complete?t():(e.onload=()=>{t()},e.onerror=o)})}function ge(e){return new Promise((t,o)=>{const l=new FileReader;l.onload=r=>{t(r.target.result)},l.onerror=o,l.readAsDataURL(e)})}function Kt(e={}){const{navigator:t=z}=e,o=["chargingchange","chargingtimechange","dischargingtimechange","levelchange"],l=W(()=>t&&"getBattery"in t&&typeof t.getBattery=="function"),r=n.shallowRef(!1),s=n.shallowRef(0),i=n.shallowRef(0),a=n.shallowRef(1);let u;function f(){r.value=this.charging,s.value=this.chargingTime||0,i.value=this.dischargingTime||0,a.value=this.level}return l.value&&t.getBattery().then(c=>{u=c,f.call(u),O(u,o,f,{passive:!0})}),{isSupported:l,charging:r,chargingTime:s,dischargingTime:i,level:a}}function Jt(e){let{acceptAllDevices:t=!1}=e||{};const{filters:o=void 0,optionalServices:l=void 0,navigator:r=z}=e||{},s=W(()=>r&&"bluetooth"in r),i=n.shallowRef(),a=n.shallowRef(null);n.watch(i,()=>{h()});async function u(){if(s.value){a.value=null,o&&o.length>0&&(t=!1);try{i.value=await r?.bluetooth.requestDevice({acceptAllDevices:t,filters:o,optionalServices:l})}catch(m){a.value=m}}}const f=n.shallowRef(),c=n.shallowRef(!1);function d(){c.value=!1,i.value=void 0,f.value=void 0}async function h(){if(a.value=null,i.value&&i.value.gatt){O(i,"gattserverdisconnected",d,{passive:!0});try{f.value=await i.value.gatt.connect(),c.value=f.value.connected}catch(m){a.value=m}}}return g.tryOnMounted(()=>{var m;i.value&&((m=i.value.gatt)==null||m.connect())}),g.tryOnScopeDispose(()=>{var m;i.value&&((m=i.value.gatt)==null||m.disconnect())}),{isSupported:s,isConnected:n.readonly(c),device:i,requestDevice:u,server:f,error:a}}const be=Symbol("vueuse-ssr-width");function Se(){const e=n.hasInjectionContext()?g.injectLocal(be,null):null;return typeof e=="number"?e:void 0}function Qt(e,t){t!==void 0?t.provide(be,e):g.provideLocal(be,e)}function $(e,t={}){const{window:o=M,ssrWidth:l=Se()}=t,r=W(()=>o&&"matchMedia"in o&&typeof o.matchMedia=="function"),s=n.shallowRef(typeof l=="number"),i=n.shallowRef(),a=n.shallowRef(!1),u=f=>{a.value=f.matches};return n.watchEffect(()=>{if(s.value){s.value=!r.value;const f=n.toValue(e).split(",");a.value=f.some(c=>{const d=c.includes("not all"),h=c.match(/\(\s*min-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/),m=c.match(/\(\s*max-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/);let v=!!(h||m);return h&&v&&(v=l>=g.pxValue(h[1])),m&&v&&(v=l<=g.pxValue(m[1])),d?!v:v});return}r.value&&(i.value=o.matchMedia(n.toValue(e)),a.value=i.value.matches)}),O(i,"change",u,{passive:!0}),n.computed(()=>a.value)}const Zt={sm:640,md:768,lg:1024,xl:1280,"2xl":1536},en={xs:0,sm:576,md:768,lg:992,xl:1200,xxl:1400},Le={xs:0,sm:600,md:960,lg:1264,xl:1904},tn={xs:0,sm:600,md:960,lg:1280,xl:1920,xxl:2560},nn=Le,on={xs:480,sm:576,md:768,lg:992,xl:1200,xxl:1600},ln={xs:0,sm:600,md:1024,lg:1440,xl:1920},an={mobileS:320,mobileM:375,mobileL:425,tablet:768,laptop:1024,laptopL:1440,desktop4K:2560},rn={"3xs":360,"2xs":480,xs:600,sm:768,md:1024,lg:1280,xl:1440,"2xl":1600,"3xl":1920,"4xl":2560},sn={sm:576,md:768,lg:992,xl:1200},un={xs:0,sm:768,md:992,lg:1200,xl:1920};function cn(e,t={}){function o(m,v){let S=n.toValue(e[n.toValue(m)]);return v!=null&&(S=g.increaseWithUnit(S,v)),typeof S=="number"&&(S=`${S}px`),S}const{window:l=M,strategy:r="min-width",ssrWidth:s=Se()}=t,i=typeof s=="number",a=i?n.shallowRef(!1):{value:!0};i&&g.tryOnMounted(()=>a.value=!!l);function u(m,v){return!a.value&&i?m==="min"?s>=g.pxValue(v):s<=g.pxValue(v):l?l.matchMedia(`(${m}-width: ${v})`).matches:!1}const f=m=>$(()=>`(min-width: ${o(m)})`,t),c=m=>$(()=>`(max-width: ${o(m)})`,t),d=Object.keys(e).reduce((m,v)=>(Object.defineProperty(m,v,{get:()=>r==="min-width"?f(v):c(v),enumerable:!0,configurable:!0}),m),{});function h(){const m=Object.keys(e).map(v=>[v,d[v],g.pxValue(o(v))]).sort((v,S)=>v[2]-S[2]);return n.computed(()=>m.filter(([,v])=>v.value).map(([v])=>v))}return Object.assign(d,{greaterOrEqual:f,smallerOrEqual:c,greater(m){return $(()=>`(min-width: ${o(m,.1)})`,t)},smaller(m){return $(()=>`(max-width: ${o(m,-.1)})`,t)},between(m,v){return $(()=>`(min-width: ${o(m)}) and (max-width: ${o(v,-.1)})`,t)},isGreater(m){return u("min",o(m,.1))},isGreaterOrEqual(m){return u("min",o(m))},isSmaller(m){return u("max",o(m,-.1))},isSmallerOrEqual(m){return u("max",o(m))},isInBetween(m,v){return u("min",o(m))&&u("max",o(v,-.1))},current:h,active(){const m=h();return n.computed(()=>m.value.length===0?"":m.value.at(r==="min-width"?-1:0))}})}function fn(e){const{name:t,window:o=M}=e,l=W(()=>o&&"BroadcastChannel"in o),r=n.shallowRef(!1),s=n.ref(),i=n.ref(),a=n.shallowRef(null),u=c=>{s.value&&s.value.postMessage(c)},f=()=>{s.value&&s.value.close(),r.value=!0};return l.value&&g.tryOnMounted(()=>{a.value=null,s.value=new BroadcastChannel(t);const c={passive:!0};O(s,"message",d=>{i.value=d.data},c),O(s,"messageerror",d=>{a.value=d},c),O(s,"close",()=>{r.value=!0},c)}),g.tryOnScopeDispose(()=>{f()}),{isSupported:l,channel:s,data:i,post:u,close:f,error:a,isClosed:r}}const Ne=["hash","host","hostname","href","pathname","port","protocol","search"];function dn(e={}){const{window:t=M}=e,o=Object.fromEntries(Ne.map(s=>[s,n.ref()]));for(const[s,i]of g.objectEntries(o))n.watch(i,a=>{!t?.location||t.location[s]===a||(t.location[s]=a)});const l=s=>{var i;const{state:a,length:u}=t?.history||{},{origin:f}=t?.location||{};for(const c of Ne)o[c].value=(i=t?.location)==null?void 0:i[c];return n.reactive({trigger:s,state:a,length:u,origin:f,...o})},r=n.ref(l("load"));if(t){const s={passive:!0};O(t,"popstate",()=>r.value=l("popstate"),s),O(t,"hashchange",()=>r.value=l("hashchange"),s)}return r}function mn(e,t=(l,r)=>l===r,o){const{deepRefs:l=!0,...r}=o||{},s=g.createRef(e.value,l);return n.watch(()=>e.value,i=>{t(i,s.value)||(s.value=i)},r),s}function se(e,t={}){const{controls:o=!1,navigator:l=z}=t,r=W(()=>l&&"permissions"in l),s=n.shallowRef(),i=typeof e=="string"?{name:e}:e,a=n.shallowRef(),u=()=>{var c,d;a.value=(d=(c=s.value)==null?void 0:c.state)!=null?d:"prompt"};O(s,"change",u,{passive:!0});const f=g.createSingletonPromise(async()=>{if(r.value){if(!s.value)try{s.value=await l.permissions.query(i)}catch{s.value=void 0}finally{u()}if(o)return n.toRaw(s.value)}});return f(),o?{state:a,isSupported:r,query:f}:a}function vn(e={}){const{navigator:t=z,read:o=!1,source:l,copiedDuring:r=1500,legacy:s=!1}=e,i=W(()=>t&&"clipboard"in t),a=se("clipboard-read"),u=se("clipboard-write"),f=n.computed(()=>i.value||s),c=n.shallowRef(""),d=n.shallowRef(!1),h=g.useTimeoutFn(()=>d.value=!1,r,{immediate:!1});async function m(){let y=!(i.value&&w(a.value));if(!y)try{c.value=await t.clipboard.readText()}catch{y=!0}y&&(c.value=p())}f.value&&o&&O(["copy","cut"],m,{passive:!0});async function v(y=n.toValue(l)){if(f.value&&y!=null){let b=!(i.value&&w(u.value));if(!b)try{await t.clipboard.writeText(y)}catch{b=!0}b&&S(y),c.value=y,d.value=!0,h.start()}}function S(y){const b=document.createElement("textarea");b.value=y??"",b.style.position="absolute",b.style.opacity="0",document.body.appendChild(b),b.select(),document.execCommand("copy"),b.remove()}function p(){var y,b,T;return(T=(b=(y=document?.getSelection)==null?void 0:y.call(document))==null?void 0:b.toString())!=null?T:""}function w(y){return y==="granted"||y==="prompt"}return{isSupported:f,text:c,copied:d,copy:v}}function pn(e={}){const{navigator:t=z,read:o=!1,source:l,copiedDuring:r=1500}=e,s=W(()=>t&&"clipboard"in t),i=n.ref([]),a=n.shallowRef(!1),u=g.useTimeoutFn(()=>a.value=!1,r,{immediate:!1});function f(){s.value&&t.clipboard.read().then(d=>{i.value=d})}s.value&&o&&O(["copy","cut"],f,{passive:!0});async function c(d=n.toValue(l)){s.value&&d!=null&&(await t.clipboard.write(d),i.value=d,a.value=!0,u.start())}return{isSupported:s,content:i,copied:a,copy:c}}function ne(e){return JSON.parse(JSON.stringify(e))}function hn(e,t={}){const o=n.ref({}),l=n.shallowRef(!1);let r=!1;const{manual:s,clone:i=ne,deep:a=!0,immediate:u=!0}=t;n.watch(o,()=>{if(r){r=!1;return}l.value=!0},{deep:!0,flush:"sync"});function f(){r=!0,l.value=!1,o.value=i(n.toValue(e))}return!s&&(n.isRef(e)||typeof e=="function")?n.watch(e,f,{...t,deep:a,immediate:u}):f(),{cloned:o,isModified:l,sync:f}}const ue=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},ce="__vueuse_ssr_handlers__",xe=yn();function yn(){return ce in ue||(ue[ce]=ue[ce]||{}),ue[ce]}function fe(e,t){return xe[e]||t}function wn(e,t){xe[e]=t}function We(e){return $("(prefers-color-scheme: dark)",e)}function He(e){return e==null?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":typeof e=="boolean"?"boolean":typeof e=="string"?"string":typeof e=="object"?"object":Number.isNaN(e)?"any":"number"}const Re={boolean:{read:e=>e==="true",write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},Ee="vueuse-storage";function de(e,t,o,l={}){var r;const{flush:s="pre",deep:i=!0,listenToStorageChanges:a=!0,writeDefaults:u=!0,mergeDefaults:f=!1,shallow:c,window:d=M,eventFilter:h,onError:m=F=>{console.error(F)},initOnMounted:v}=l,S=(c?n.shallowRef:n.ref)(typeof t=="function"?t():t),p=n.computed(()=>n.toValue(e));if(!o)try{o=fe("getDefaultStorage",()=>{var F;return(F=M)==null?void 0:F.localStorage})()}catch(F){m(F)}if(!o)return S;const w=n.toValue(t),y=He(w),b=(r=l.serializer)!=null?r:Re[y],{pause:T,resume:k}=g.pausableWatch(S,()=>I(S.value),{flush:s,deep:i,eventFilter:h});n.watch(p,()=>_(),{flush:s});let C=!1;const R=F=>{v&&!C||_(F)},V=F=>{v&&!C||D(F)};d&&a&&(o instanceof Storage?O(d,"storage",R,{passive:!0}):O(d,Ee,V)),v?g.tryOnMounted(()=>{C=!0,_()}):_();function P(F,N){if(d){const H={key:p.value,oldValue:F,newValue:N,storageArea:o};d.dispatchEvent(o instanceof Storage?new StorageEvent("storage",H):new CustomEvent(Ee,{detail:H}))}}function I(F){try{const N=o.getItem(p.value);if(F==null)P(N,null),o.removeItem(p.value);else{const H=b.write(F);N!==H&&(o.setItem(p.value,H),P(N,H))}}catch(N){m(N)}}function L(F){const N=F?F.newValue:o.getItem(p.value);if(N==null)return u&&w!=null&&o.setItem(p.value,b.write(w)),w;if(!F&&f){const H=b.read(N);return typeof f=="function"?f(H,w):y==="object"&&!Array.isArray(H)?{...w,...H}:H}else return typeof N!="string"?N:b.read(N)}function _(F){if(!(F&&F.storageArea!==o)){if(F&&F.key==null){S.value=w;return}if(!(F&&F.key!==p.value)){T();try{F?.newValue!==b.write(S.value)&&(S.value=L(F))}catch(N){m(N)}finally{F?n.nextTick(k):k()}}}}function D(F){_(F.detail)}return S}const gn="*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";function Ue(e={}){const{selector:t="html",attribute:o="class",initialValue:l="auto",window:r=M,storage:s,storageKey:i="vueuse-color-scheme",listenToStorageChanges:a=!0,storageRef:u,emitAuto:f,disableTransition:c=!0}=e,d={auto:"",light:"light",dark:"dark",...e.modes||{}},h=We({window:r}),m=n.computed(()=>h.value?"dark":"light"),v=u||(i==null?g.toRef(l):de(i,l,s,{window:r,listenToStorageChanges:a})),S=n.computed(()=>v.value==="auto"?m.value:v.value),p=fe("updateHTMLAttrs",(T,k,C)=>{const R=typeof T=="string"?r?.document.querySelector(T):x(T);if(!R)return;const V=new Set,P=new Set;let I=null;if(k==="class"){const _=C.split(/\s/g);Object.values(d).flatMap(D=>(D||"").split(/\s/g)).filter(Boolean).forEach(D=>{_.includes(D)?V.add(D):P.add(D)})}else I={key:k,value:C};if(V.size===0&&P.size===0&&I===null)return;let L;c&&(L=r.document.createElement("style"),L.appendChild(document.createTextNode(gn)),r.document.head.appendChild(L));for(const _ of V)R.classList.add(_);for(const _ of P)R.classList.remove(_);I&&R.setAttribute(I.key,I.value),c&&(r.getComputedStyle(L).opacity,document.head.removeChild(L))});function w(T){var k;p(t,o,(k=d[T])!=null?k:T)}function y(T){e.onChanged?e.onChanged(T,w):w(T)}n.watch(S,y,{flush:"post",immediate:!0}),g.tryOnMounted(()=>y(S.value));const b=n.computed({get(){return f?v.value:S.value},set(T){v.value=T}});return Object.assign(b,{store:v,system:m,state:S})}function bn(e=n.shallowRef(!1)){const t=g.createEventHook(),o=g.createEventHook(),l=g.createEventHook();let r=g.noop;const s=u=>(l.trigger(u),e.value=!0,new Promise(f=>{r=f})),i=u=>{e.value=!1,t.trigger(u),r({data:u,isCanceled:!1})},a=u=>{e.value=!1,o.trigger(u),r({data:u,isCanceled:!0})};return{isRevealed:n.computed(()=>e.value),reveal:s,confirm:i,cancel:a,onReveal:l.on,onConfirm:t.on,onCancel:o.on}}function Sn(e,t){var o,l;const r=n.shallowRef(n.toValue(e)),s=g.useIntervalFn(()=>{var c,d;const h=r.value-1;r.value=h<0?0:h,(c=t?.onTick)==null||c.call(t),r.value<=0&&(s.pause(),(d=t?.onComplete)==null||d.call(t))},(o=t?.interval)!=null?o:1e3,{immediate:(l=t?.immediate)!=null?l:!1}),i=c=>{var d;r.value=(d=n.toValue(c))!=null?d:n.toValue(e)},a=()=>{s.pause(),i()},u=()=>{s.isActive.value||r.value>0&&s.resume()};return{remaining:r,reset:i,stop:a,start:c=>{i(c),s.resume()},pause:s.pause,resume:u,isActive:s.isActive}}function oe(e,t,o={}){const{window:l=M,initialValue:r,observe:s=!1}=o,i=n.shallowRef(r),a=n.computed(()=>{var f;return x(t)||((f=l?.document)==null?void 0:f.documentElement)});function u(){var f;const c=n.toValue(e),d=n.toValue(a);if(d&&l&&c){const h=(f=l.getComputedStyle(d).getPropertyValue(c))==null?void 0:f.trim();i.value=h||i.value||r}}return s&&Q(a,u,{attributeFilter:["style","class"],window:l}),n.watch([a,()=>n.toValue(e)],(f,c)=>{c[0]&&c[1]&&c[0].style.removeProperty(c[1]),u()},{immediate:!0}),n.watch([i,a],([f,c])=>{const d=n.toValue(e);c?.style&&d&&(f==null?c.style.removeProperty(d):c.style.setProperty(d,f))},{immediate:!0}),i}function $e(e){const t=n.getCurrentInstance(),o=g.computedWithControl(()=>null,()=>e?x(e):t.proxy.$el);return n.onUpdated(o.trigger),n.onMounted(o.trigger),o}function Rn(e,t){const o=n.shallowRef(f()),l=g.toRef(e),r=n.computed({get(){var c;const d=l.value;let h=t?.getIndexOf?t.getIndexOf(o.value,d):d.indexOf(o.value);return h<0&&(h=(c=t?.fallbackIndex)!=null?c:0),h},set(c){s(c)}});function s(c){const d=l.value,h=d.length,m=(c%h+h)%h,v=d[m];return o.value=v,v}function i(c=1){return s(r.value+c)}function a(c=1){return i(c)}function u(c=1){return i(-c)}function f(){var c,d;return(d=n.toValue((c=t?.initialValue)!=null?c:n.toValue(e)[0]))!=null?d:void 0}return n.watch(l,()=>s(r.value)),{state:o,index:r,next:a,prev:u,go:s}}function En(e={}){const{valueDark:t="dark",valueLight:o=""}=e,l=Ue({...e,onChanged:(i,a)=>{var u;e.onChanged?(u=e.onChanged)==null||u.call(e,i==="dark",a,i):a(i)},modes:{dark:t,light:o}}),r=n.computed(()=>l.system.value);return n.computed({get(){return l.value==="dark"},set(i){const a=i?"dark":"light";r.value===a?l.value="auto":l.value=a}})}function Be(e){return e}function Tn(e,t){return e.value=t}function On(e){return e?typeof e=="function"?e:ne:Be}function kn(e){return e?typeof e=="function"?e:ne:Be}function je(e,t={}){const{clone:o=!1,dump:l=On(o),parse:r=kn(o),setSource:s=Tn}=t;function i(){return n.markRaw({snapshot:l(e.value),timestamp:g.timestamp()})}const a=n.ref(i()),u=n.ref([]),f=n.ref([]),c=b=>{s(e,r(b.snapshot)),a.value=b},d=()=>{u.value.unshift(a.value),a.value=i(),t.capacity&&u.value.length>t.capacity&&u.value.splice(t.capacity,Number.POSITIVE_INFINITY),f.value.length&&f.value.splice(0,f.value.length)},h=()=>{u.value.splice(0,u.value.length),f.value.splice(0,f.value.length)},m=()=>{const b=u.value.shift();b&&(f.value.unshift(a.value),c(b))},v=()=>{const b=f.value.shift();b&&(u.value.unshift(a.value),c(b))},S=()=>{c(a.value)},p=n.computed(()=>[a.value,...u.value]),w=n.computed(()=>u.value.length>0),y=n.computed(()=>f.value.length>0);return{source:e,undoStack:u,redoStack:f,last:a,history:p,canUndo:w,canRedo:y,clear:h,commit:d,reset:S,undo:m,redo:v}}function Te(e,t={}){const{deep:o=!1,flush:l="pre",eventFilter:r}=t,{eventFilter:s,pause:i,resume:a,isActive:u}=g.pausableFilter(r),{ignoreUpdates:f,ignorePrevAsyncUpdates:c,stop:d}=g.watchIgnorable(e,p,{deep:o,flush:l,eventFilter:s});function h(T,k){c(),f(()=>{T.value=k})}const m=je(e,{...t,clone:t.clone||o,setSource:h}),{clear:v,commit:S}=m;function p(){c(),S()}function w(T){a(),T&&p()}function y(T){let k=!1;const C=()=>k=!0;f(()=>{T(C)}),k||p()}function b(){d(),v()}return{...m,isTracking:u,pause:i,resume:w,commit:p,batch:y,dispose:b}}function _n(e,t={}){const o=t.debounce?g.debounceFilter(t.debounce):void 0;return{...Te(e,{...t,eventFilter:o})}}function Vn(e={}){const{window:t=M,requestPermissions:o=!1,eventFilter:l=g.bypassFilter}=e,r=W(()=>typeof DeviceMotionEvent<"u"),s=W(()=>r.value&&"requestPermission"in DeviceMotionEvent&&typeof DeviceMotionEvent.requestPermission=="function"),i=n.shallowRef(!1),a=n.ref({x:null,y:null,z:null}),u=n.ref({alpha:null,beta:null,gamma:null}),f=n.shallowRef(0),c=n.ref({x:null,y:null,z:null});function d(){if(t){const m=g.createFilterWrapper(l,v=>{var S,p,w,y,b,T,k,C,R;a.value={x:((S=v.acceleration)==null?void 0:S.x)||null,y:((p=v.acceleration)==null?void 0:p.y)||null,z:((w=v.acceleration)==null?void 0:w.z)||null},c.value={x:((y=v.accelerationIncludingGravity)==null?void 0:y.x)||null,y:((b=v.accelerationIncludingGravity)==null?void 0:b.y)||null,z:((T=v.accelerationIncludingGravity)==null?void 0:T.z)||null},u.value={alpha:((k=v.rotationRate)==null?void 0:k.alpha)||null,beta:((C=v.rotationRate)==null?void 0:C.beta)||null,gamma:((R=v.rotationRate)==null?void 0:R.gamma)||null},f.value=v.interval});O(t,"devicemotion",m,{passive:!0})}}const h=async()=>{if(s.value||(i.value=!0),!i.value&&s.value){const m=DeviceMotionEvent.requestPermission;try{await m()==="granted"&&(i.value=!0,d())}catch(v){console.error(v)}}};return r.value&&(o&&s.value?h().then(()=>d()):d()),{acceleration:a,accelerationIncludingGravity:c,rotationRate:u,interval:f,isSupported:r,requirePermissions:s,ensurePermissions:h,permissionGranted:i}}function ze(e={}){const{window:t=M}=e,o=W(()=>t&&"DeviceOrientationEvent"in t),l=n.shallowRef(!1),r=n.shallowRef(null),s=n.shallowRef(null),i=n.shallowRef(null);return t&&o.value&&O(t,"deviceorientation",a=>{l.value=a.absolute,r.value=a.alpha,s.value=a.beta,i.value=a.gamma},{passive:!0}),{isSupported:o,isAbsolute:l,alpha:r,beta:s,gamma:i}}function Fn(e={}){const{window:t=M}=e,o=n.shallowRef(1),l=$(()=>`(resolution: ${o.value}dppx)`,e);let r=g.noop;return t&&(r=g.watchImmediate(l,()=>o.value=t.devicePixelRatio)),{pixelRatio:n.readonly(o),stop:r}}function Pn(e={}){const{navigator:t=z,requestPermissions:o=!1,constraints:l={audio:!0,video:!0},onUpdated:r}=e,s=n.ref([]),i=n.computed(()=>s.value.filter(v=>v.kind==="videoinput")),a=n.computed(()=>s.value.filter(v=>v.kind==="audioinput")),u=n.computed(()=>s.value.filter(v=>v.kind==="audiooutput")),f=W(()=>t&&t.mediaDevices&&t.mediaDevices.enumerateDevices),c=n.shallowRef(!1);let d;async function h(){f.value&&(s.value=await t.mediaDevices.enumerateDevices(),r?.(s.value),d&&(d.getTracks().forEach(v=>v.stop()),d=null))}async function m(){const v=l.video?"camera":"microphone";if(!f.value)return!1;if(c.value)return!0;const{state:S,query:p}=se(v,{controls:!0});if(await p(),S.value!=="granted"){let w=!0;try{d=await t.mediaDevices.getUserMedia(l)}catch{d=null,w=!1}h(),c.value=w}else c.value=!0;return c.value}return f.value&&(o&&m(),O(t.mediaDevices,"devicechange",h,{passive:!0}),h()),{devices:s,ensurePermissions:m,permissionGranted:c,videoInputs:i,audioInputs:a,audioOutputs:u,isSupported:f}}function Cn(e={}){var t;const o=n.shallowRef((t=e.enabled)!=null?t:!1),l=e.video,r=e.audio,{navigator:s=z}=e,i=W(()=>{var m;return(m=s?.mediaDevices)==null?void 0:m.getDisplayMedia}),a={audio:r,video:l},u=n.shallowRef();async function f(){var m;if(!(!i.value||u.value))return u.value=await s.mediaDevices.getDisplayMedia(a),(m=u.value)==null||m.getTracks().forEach(v=>O(v,"ended",d,{passive:!0})),u.value}async function c(){var m;(m=u.value)==null||m.getTracks().forEach(v=>v.stop()),u.value=void 0}function d(){c(),o.value=!1}async function h(){return await f(),u.value&&(o.value=!0),u.value}return n.watch(o,m=>{m?f():c()},{immediate:!0}),{isSupported:i,stream:u,start:h,stop:d,enabled:o}}function qe(e={}){const{document:t=j}=e;if(!t)return n.shallowRef("visible");const o=n.shallowRef(t.visibilityState);return O(t,"visibilitychange",()=>{o.value=t.visibilityState},{passive:!0}),o}function Dn(e,t={}){var o;const{pointerTypes:l,preventDefault:r,stopPropagation:s,exact:i,onMove:a,onEnd:u,onStart:f,initialValue:c,axis:d="both",draggingElement:h=M,containerElement:m,handle:v=e,buttons:S=[0]}=t,p=n.ref((o=n.toValue(c))!=null?o:{x:0,y:0}),w=n.ref(),y=R=>l?l.includes(R.pointerType):!0,b=R=>{n.toValue(r)&&R.preventDefault(),n.toValue(s)&&R.stopPropagation()},T=R=>{var V;if(!n.toValue(S).includes(R.button)||n.toValue(t.disabled)||!y(R)||n.toValue(i)&&R.target!==n.toValue(e))return;const P=n.toValue(m),I=(V=P?.getBoundingClientRect)==null?void 0:V.call(P),L=n.toValue(e).getBoundingClientRect(),_={x:R.clientX-(P?L.left-I.left+P.scrollLeft:L.left),y:R.clientY-(P?L.top-I.top+P.scrollTop:L.top)};f?.(_,R)!==!1&&(w.value=_,b(R))},k=R=>{if(n.toValue(t.disabled)||!y(R)||!w.value)return;const V=n.toValue(m),P=n.toValue(e).getBoundingClientRect();let{x:I,y:L}=p.value;(d==="x"||d==="both")&&(I=R.clientX-w.value.x,V&&(I=Math.min(Math.max(0,I),V.scrollWidth-P.width))),(d==="y"||d==="both")&&(L=R.clientY-w.value.y,V&&(L=Math.min(Math.max(0,L),V.scrollHeight-P.height))),p.value={x:I,y:L},a?.(p.value,R),b(R)},C=R=>{n.toValue(t.disabled)||!y(R)||w.value&&(w.value=void 0,u?.(p.value,R),b(R))};if(g.isClient){const R=()=>{var V;return{capture:(V=t.capture)!=null?V:!0,passive:!n.toValue(r)}};O(v,"pointerdown",T,R),O(h,"pointermove",k,R),O(h,"pointerup",C,R)}return{...g.toRefs(p),position:p,isDragging:n.computed(()=>!!w.value),style:n.computed(()=>`left:${p.value.x}px;top:${p.value.y}px;`)}}function An(e,t={}){var o,l;const r=n.shallowRef(!1),s=n.shallowRef(null);let i=0,a=!0;if(g.isClient){const u=typeof t=="function"?{onDrop:t}:t,f=(o=u.multiple)!=null?o:!0,c=(l=u.preventDefaultForUnhandled)!=null?l:!1,d=p=>{var w,y;const b=Array.from((y=(w=p.dataTransfer)==null?void 0:w.files)!=null?y:[]);return b.length===0?null:f?b:[b[0]]},h=p=>{const w=n.unref(u.dataTypes);return typeof w=="function"?w(p):w?.length?p.length===0?!1:p.every(y=>w.some(b=>y.includes(b))):!0},m=p=>{const w=Array.from(p??[]).map(T=>T.type),y=h(w),b=f||p.length<=1;return y&&b},v=()=>/^(?:(?!chrome|android).)*safari/i.test(navigator.userAgent)&&!("chrome"in window),S=(p,w)=>{var y,b,T,k,C,R;const V=(y=p.dataTransfer)==null?void 0:y.items;if(a=(b=V&&m(V))!=null?b:!1,c&&p.preventDefault(),!v()&&!a){p.dataTransfer&&(p.dataTransfer.dropEffect="none");return}p.preventDefault(),p.dataTransfer&&(p.dataTransfer.dropEffect="copy");const P=d(p);switch(w){case"enter":i+=1,r.value=!0,(T=u.onEnter)==null||T.call(u,null,p);break;case"over":(k=u.onOver)==null||k.call(u,null,p);break;case"leave":i-=1,i===0&&(r.value=!1),(C=u.onLeave)==null||C.call(u,null,p);break;case"drop":i=0,r.value=!1,a&&(s.value=P,(R=u.onDrop)==null||R.call(u,P,p));break}};O(e,"dragenter",p=>S(p,"enter")),O(e,"dragover",p=>S(p,"over")),O(e,"dragleave",p=>S(p,"leave")),O(e,"drop",p=>S(p,"drop"))}return{files:s,isOverDropZone:r}}function me(e,t,o={}){const{window:l=M,...r}=o;let s;const i=W(()=>l&&"ResizeObserver"in l),a=()=>{s&&(s.disconnect(),s=void 0)},u=n.computed(()=>{const d=n.toValue(e);return Array.isArray(d)?d.map(h=>x(h)):[x(d)]}),f=n.watch(u,d=>{if(a(),i.value&&l){s=new ResizeObserver(t);for(const h of d)h&&s.observe(h,r)}},{immediate:!0,flush:"post"}),c=()=>{a(),f()};return g.tryOnScopeDispose(c),{isSupported:i,stop:c}}function Mn(e,t={}){const{reset:o=!0,windowResize:l=!0,windowScroll:r=!0,immediate:s=!0,updateTiming:i="sync"}=t,a=n.shallowRef(0),u=n.shallowRef(0),f=n.shallowRef(0),c=n.shallowRef(0),d=n.shallowRef(0),h=n.shallowRef(0),m=n.shallowRef(0),v=n.shallowRef(0);function S(){const w=x(e);if(!w){o&&(a.value=0,u.value=0,f.value=0,c.value=0,d.value=0,h.value=0,m.value=0,v.value=0);return}const y=w.getBoundingClientRect();a.value=y.height,u.value=y.bottom,f.value=y.left,c.value=y.right,d.value=y.top,h.value=y.width,m.value=y.x,v.value=y.y}function p(){i==="sync"?S():i==="next-frame"&&requestAnimationFrame(()=>S())}return me(e,p),n.watch(()=>x(e),w=>!w&&p()),Q(e,p,{attributeFilter:["style","class"]}),r&&O("scroll",p,{capture:!0,passive:!0}),l&&O("resize",p,{passive:!0}),g.tryOnMounted(()=>{s&&p()}),{height:a,bottom:u,left:f,right:c,top:d,width:h,x:m,y:v,update:p}}function In(e){const{x:t,y:o,document:l=j,multiple:r,interval:s="requestAnimationFrame",immediate:i=!0}=e,a=W(()=>n.toValue(r)?l&&"elementsFromPoint"in l:l&&"elementFromPoint"in l),u=n.shallowRef(null),f=()=>{var d,h;u.value=n.toValue(r)?(d=l?.elementsFromPoint(n.toValue(t),n.toValue(o)))!=null?d:[]:(h=l?.elementFromPoint(n.toValue(t),n.toValue(o)))!=null?h:null},c=s==="requestAnimationFrame"?Y(f,{immediate:i}):g.useIntervalFn(f,s,{immediate:i});return{isSupported:a,element:u,...c}}function Ln(e,t={}){const{delayEnter:o=0,delayLeave:l=0,triggerOnRemoval:r=!1,window:s=M}=t,i=n.shallowRef(!1);let a;const u=f=>{const c=f?o:l;a&&(clearTimeout(a),a=void 0),c?a=setTimeout(()=>i.value=f,c):i.value=f};return s&&(O(e,"mouseenter",()=>u(!0),{passive:!0}),O(e,"mouseleave",()=>u(!1),{passive:!0}),r&&we(n.computed(()=>x(e)),()=>u(!1))),i}function Ge(e,t={width:0,height:0},o={}){const{window:l=M,box:r="content-box"}=o,s=n.computed(()=>{var d,h;return(h=(d=x(e))==null?void 0:d.namespaceURI)==null?void 0:h.includes("svg")}),i=n.shallowRef(t.width),a=n.shallowRef(t.height),{stop:u}=me(e,([d])=>{const h=r==="border-box"?d.borderBoxSize:r==="content-box"?d.contentBoxSize:d.devicePixelContentBoxSize;if(l&&s.value){const m=x(e);if(m){const v=m.getBoundingClientRect();i.value=v.width,a.value=v.height}}else if(h){const m=g.toArray(h);i.value=m.reduce((v,{inlineSize:S})=>v+S,0),a.value=m.reduce((v,{blockSize:S})=>v+S,0)}else i.value=d.contentRect.width,a.value=d.contentRect.height},o);g.tryOnMounted(()=>{const d=x(e);d&&(i.value="offsetWidth"in d?d.offsetWidth:t.width,a.value="offsetHeight"in d?d.offsetHeight:t.height)});const f=n.watch(()=>x(e),d=>{i.value=d?t.width:0,a.value=d?t.height:0});function c(){u(),f()}return{width:i,height:a,stop:c}}function Ye(e,t,o={}){const{root:l,rootMargin:r="0px",threshold:s=0,window:i=M,immediate:a=!0}=o,u=W(()=>i&&"IntersectionObserver"in i),f=n.computed(()=>{const v=n.toValue(e);return g.toArray(v).map(x).filter(g.notNullish)});let c=g.noop;const d=n.shallowRef(a),h=u.value?n.watch(()=>[f.value,x(l),d.value],([v,S])=>{if(c(),!d.value||!v.length)return;const p=new IntersectionObserver(t,{root:x(S),rootMargin:r,threshold:s});v.forEach(w=>w&&p.observe(w)),c=()=>{p.disconnect(),c=g.noop}},{immediate:a,flush:"post"}):g.noop,m=()=>{c(),h(),d.value=!1};return g.tryOnScopeDispose(m),{isSupported:u,isActive:d,pause(){c(),d.value=!1},resume(){d.value=!0},stop:m}}function Xe(e,t={}){const{window:o=M,scrollTarget:l,threshold:r=0,rootMargin:s,once:i=!1}=t,a=n.shallowRef(!1),{stop:u}=Ye(e,f=>{let c=a.value,d=0;for(const h of f)h.time>=d&&(d=h.time,c=h.isIntersecting);a.value=c,i&&g.watchOnce(a,()=>{u()})},{root:l,window:o,threshold:r,rootMargin:n.toValue(s)});return a}const le=new Map;function Nn(e){const t=n.getCurrentScope();function o(a){var u;const f=le.get(e)||new Set;f.add(a),le.set(e,f);const c=()=>r(a);return(u=t?.cleanups)==null||u.push(c),c}function l(a){function u(...f){r(u),a(...f)}return o(u)}function r(a){const u=le.get(e);u&&(u.delete(a),u.size||s())}function s(){le.delete(e)}function i(a,u){var f;(f=le.get(e))==null||f.forEach(c=>c(a,u))}return{on:o,once:l,off:r,emit:i,reset:s}}function xn(e){return e===!0?{}:e}function Wn(e,t=[],o={}){const l=n.shallowRef(null),r=n.shallowRef(null),s=n.shallowRef("CONNECTING"),i=n.ref(null),a=n.shallowRef(null),u=g.toRef(e),f=n.shallowRef(null);let c=!1,d=0;const{withCredentials:h=!1,immediate:m=!0,autoConnect:v=!0,autoReconnect:S}=o,p=()=>{g.isClient&&i.value&&(i.value.close(),i.value=null,s.value="CLOSED",c=!0)},w=()=>{if(c||typeof u.value>"u")return;const b=new EventSource(u.value,{withCredentials:h});s.value="CONNECTING",i.value=b,b.onopen=()=>{s.value="OPEN",a.value=null},b.onerror=T=>{if(s.value="CLOSED",a.value=T,b.readyState===2&&!c&&S){b.close();const{retries:k=-1,delay:C=1e3,onFailed:R}=xn(S);d+=1,typeof k=="number"&&(k<0||d<k)||typeof k=="function"&&k()?setTimeout(w,C):R?.()}},b.onmessage=T=>{l.value=null,r.value=T.data,f.value=T.lastEventId};for(const T of t)O(b,T,k=>{l.value=T,r.value=k.data||null},{passive:!0})},y=()=>{g.isClient&&(p(),c=!1,d=0,w())};return m&&y(),v&&n.watch(u,y),g.tryOnScopeDispose(p),{eventSource:i,event:l,data:r,status:s,error:a,open:y,close:p,lastEventId:f}}function Hn(e={}){const{initialValue:t=""}=e,o=W(()=>typeof window<"u"&&"EyeDropper"in window),l=n.shallowRef(t);async function r(s){if(!o.value)return;const a=await new window.EyeDropper().open(s);return l.value=a.sRGBHex,a}return{isSupported:o,sRGBHex:l,open:r}}function Un(e=null,t={}){const{baseUrl:o="",rel:l="icon",document:r=j}=t,s=g.toRef(e),i=a=>{const u=r?.head.querySelectorAll(`link[rel*="${l}"]`);if(!u||u.length===0){const f=r?.createElement("link");f&&(f.rel=l,f.href=`${o}${a}`,f.type=`image/${a.split(".").pop()}`,r?.head.append(f));return}u?.forEach(f=>f.href=`${o}${a}`)};return n.watch(s,(a,u)=>{typeof a=="string"&&a!==u&&i(a)},{immediate:!0}),s}const $n={json:"application/json",text:"text/plain"};function ve(e){return e&&g.containsProp(e,"immediate","refetch","initialData","timeout","beforeFetch","afterFetch","onFetchError","fetch","updateDataOnError")}const Bn=/^(?:[a-z][a-z\d+\-.]*:)?\/\//i;function jn(e){return Bn.test(e)}function ae(e){return typeof Headers<"u"&&e instanceof Headers?Object.fromEntries(e.entries()):e}function Z(e,...t){return e==="overwrite"?async o=>{let l;for(let r=t.length-1;r>=0;r--)if(t[r]!=null){l=t[r];break}return l?{...o,...await l(o)}:o}:async o=>{for(const l of t)l&&(o={...o,...await l(o)});return o}}function zn(e={}){const t=e.combination||"chain",o=e.options||{},l=e.fetchOptions||{};function r(s,...i){const a=n.computed(()=>{const c=n.toValue(e.baseUrl),d=n.toValue(s);return c&&!jn(d)?qn(c,d):d});let u=o,f=l;return i.length>0&&(ve(i[0])?u={...u,...i[0],beforeFetch:Z(t,o.beforeFetch,i[0].beforeFetch),afterFetch:Z(t,o.afterFetch,i[0].afterFetch),onFetchError:Z(t,o.onFetchError,i[0].onFetchError)}:f={...f,...i[0],headers:{...ae(f.headers)||{},...ae(i[0].headers)||{}}}),i.length>1&&ve(i[1])&&(u={...u,...i[1],beforeFetch:Z(t,o.beforeFetch,i[1].beforeFetch),afterFetch:Z(t,o.afterFetch,i[1].afterFetch),onFetchError:Z(t,o.onFetchError,i[1].onFetchError)}),Ke(a,f,u)}return r}function Ke(e,...t){var o,l;const r=typeof AbortController=="function";let s={},i={immediate:!0,refetch:!1,timeout:0,updateDataOnError:!1};const a={method:"GET",type:"text",payload:void 0};t.length>0&&(ve(t[0])?i={...i,...t[0]}:s=t[0]),t.length>1&&ve(t[1])&&(i={...i,...t[1]});const{fetch:u=(l=(o=M)==null?void 0:o.fetch)!=null?l:globalThis?.fetch,initialData:f,timeout:c}=i,d=g.createEventHook(),h=g.createEventHook(),m=g.createEventHook(),v=n.shallowRef(!1),S=n.shallowRef(!1),p=n.shallowRef(!1),w=n.shallowRef(null),y=n.shallowRef(null),b=n.shallowRef(null),T=n.shallowRef(f||null),k=n.computed(()=>r&&S.value);let C,R;const V=()=>{r&&(C?.abort(),C=new AbortController,C.signal.onabort=()=>p.value=!0,s={...s,signal:C.signal})},P=A=>{S.value=A,v.value=!A};c&&(R=g.useTimeoutFn(V,c,{immediate:!1}));let I=0;const L=async(A=!1)=>{var U,G;V(),P(!0),b.value=null,w.value=null,p.value=!1,I+=1;const q=I,X={method:a.method,headers:{}},ee=n.toValue(a.payload);if(ee){const B=ae(X.headers),re=Object.getPrototypeOf(ee);!a.payloadType&&ee&&(re===Object.prototype||Array.isArray(re))&&!(ee instanceof FormData)&&(a.payloadType="json"),a.payloadType&&(B["Content-Type"]=(U=$n[a.payloadType])!=null?U:a.payloadType),X.body=a.payloadType==="json"?JSON.stringify(ee):ee}let Ot=!1;const K={url:n.toValue(e),options:{...X,...s},cancel:()=>{Ot=!0}};if(i.beforeFetch&&Object.assign(K,await i.beforeFetch(K)),Ot||!u)return P(!1),Promise.resolve(null);let J=null;return R&&R.start(),u(K.url,{...X,...K.options,headers:{...ae(X.headers),...ae((G=K.options)==null?void 0:G.headers)}}).then(async B=>{if(y.value=B,w.value=B.status,J=await B.clone()[a.type](),!B.ok)throw T.value=f||null,new Error(B.statusText);return i.afterFetch&&({data:J}=await i.afterFetch({data:J,response:B,context:K,execute:L})),T.value=J,d.trigger(B),B}).catch(async B=>{let re=B.message||B.name;if(i.onFetchError&&({error:re,data:J}=await i.onFetchError({data:J,error:B,response:y.value,context:K,execute:L})),b.value=re,i.updateDataOnError&&(T.value=J),h.trigger(B),A)throw B;return null}).finally(()=>{q===I&&P(!1),R&&R.stop(),m.trigger(null)})},_=g.toRef(i.refetch);n.watch([_,g.toRef(e)],([A])=>A&&L(),{deep:!0});const D={isFinished:n.readonly(v),isFetching:n.readonly(S),statusCode:w,response:y,error:b,data:T,canAbort:k,aborted:p,abort:V,execute:L,onFetchResponse:d.on,onFetchError:h.on,onFetchFinally:m.on,get:F("GET"),put:F("PUT"),post:F("POST"),delete:F("DELETE"),patch:F("PATCH"),head:F("HEAD"),options:F("OPTIONS"),json:H("json"),text:H("text"),blob:H("blob"),arrayBuffer:H("arrayBuffer"),formData:H("formData")};function F(A){return(U,G)=>{if(!S.value)return a.method=A,a.payload=U,a.payloadType=G,n.isRef(a.payload)&&n.watch([_,g.toRef(a.payload)],([q])=>q&&L(),{deep:!0}),{...D,then(q,X){return N().then(q,X)}}}}function N(){return new Promise((A,U)=>{g.until(v).toBe(!0).then(()=>A(D)).catch(U)})}function H(A){return()=>{if(!S.value)return a.type=A,{...D,then(U,G){return N().then(U,G)}}}}return i.immediate&&Promise.resolve().then(()=>L()),{...D,then(A,U){return N().then(A,U)}}}function qn(e,t){return!e.endsWith("/")&&!t.startsWith("/")?`${e}/${t}`:e.endsWith("/")&&t.startsWith("/")?`${e.slice(0,-1)}${t}`:`${e}${t}`}const Gn={multiple:!0,accept:"*",reset:!1,directory:!1};function Yn(e){if(!e)return null;if(e instanceof FileList)return e;const t=new DataTransfer;for(const o of e)t.items.add(o);return t.files}function Xn(e={}){const{document:t=j}=e,o=n.ref(Yn(e.initialFiles)),{on:l,trigger:r}=g.createEventHook(),{on:s,trigger:i}=g.createEventHook();let a;t&&(a=x(e.input)||t.createElement("input"),a.type="file",a.onchange=c=>{const d=c.target;o.value=d.files,r(o.value)},a.oncancel=()=>{i()});const u=()=>{o.value=null,a&&a.value&&(a.value="",r(null))},f=c=>{if(!a)return;const d={...Gn,...e,...c};a.multiple=d.multiple,a.accept=d.accept,a.webkitdirectory=d.directory,g.hasOwn(d,"capture")&&(a.capture=d.capture),d.reset&&u(),a.click()};return{files:n.readonly(o),open:f,reset:u,onCancel:s,onChange:l}}function Kn(e={}){const{window:t=M,dataType:o="Text"}=e,l=t,r=W(()=>l&&"showSaveFilePicker"in l&&"showOpenFilePicker"in l),s=n.shallowRef(),i=n.shallowRef(),a=n.shallowRef(),u=n.computed(()=>{var y,b;return(b=(y=a.value)==null?void 0:y.name)!=null?b:""}),f=n.computed(()=>{var y,b;return(b=(y=a.value)==null?void 0:y.type)!=null?b:""}),c=n.computed(()=>{var y,b;return(b=(y=a.value)==null?void 0:y.size)!=null?b:0}),d=n.computed(()=>{var y,b;return(b=(y=a.value)==null?void 0:y.lastModified)!=null?b:0});async function h(y={}){if(!r.value)return;const[b]=await l.showOpenFilePicker({...n.toValue(e),...y});s.value=b,await w()}async function m(y={}){r.value&&(s.value=await l.showSaveFilePicker({...e,...y}),i.value=void 0,await w())}async function v(y={}){if(r.value){if(!s.value)return S(y);if(i.value){const b=await s.value.createWritable();await b.write(i.value),await b.close()}await p()}}async function S(y={}){if(r.value){if(s.value=await l.showSaveFilePicker({...e,...y}),i.value){const b=await s.value.createWritable();await b.write(i.value),await b.close()}await p()}}async function p(){var y;a.value=await((y=s.value)==null?void 0:y.getFile())}async function w(){var y,b;await p();const T=n.toValue(o);T==="Text"?i.value=await((y=a.value)==null?void 0:y.text()):T==="ArrayBuffer"?i.value=await((b=a.value)==null?void 0:b.arrayBuffer()):T==="Blob"&&(i.value=a.value)}return n.watch(()=>n.toValue(o),w),{isSupported:r,data:i,file:a,fileName:u,fileMIME:f,fileSize:c,fileLastModified:d,open:h,create:m,save:v,saveAs:S,updateData:w}}function Jn(e,t={}){const{initialValue:o=!1,focusVisible:l=!1,preventScroll:r=!1}=t,s=n.shallowRef(!1),i=n.computed(()=>x(e)),a={passive:!0};O(i,"focus",f=>{var c,d;(!l||(d=(c=f.target).matches)!=null&&d.call(c,":focus-visible"))&&(s.value=!0)},a),O(i,"blur",()=>s.value=!1,a);const u=n.computed({get:()=>s.value,set(f){var c,d;!f&&s.value?(c=i.value)==null||c.blur():f&&!s.value&&((d=i.value)==null||d.focus({preventScroll:r}))}});return n.watch(i,()=>{u.value=o},{immediate:!0,flush:"post"}),{focused:u}}const Qn="focusin",Zn="focusout",eo=":focus-within";function to(e,t={}){const{window:o=M}=t,l=n.computed(()=>x(e)),r=n.shallowRef(!1),s=n.computed(()=>r.value),i=Me(t);if(!o||!i.value)return{focused:s};const a={passive:!0};return O(l,Qn,()=>r.value=!0,a),O(l,Zn,()=>{var u,f,c;return r.value=(c=(f=(u=l.value)==null?void 0:u.matches)==null?void 0:f.call(u,eo))!=null?c:!1},a),{focused:s}}function no(e){var t;const o=n.shallowRef(0);if(typeof performance>"u")return o;const l=(t=e?.every)!=null?t:10;let r=performance.now(),s=0;return Y(()=>{if(s+=1,s>=l){const i=performance.now(),a=i-r;o.value=Math.round(1e3/(a/s)),r=i,s=0}}),o}const Je=["fullscreenchange","webkitfullscreenchange","webkitendfullscreen","mozfullscreenchange","MSFullscreenChange"];function oo(e,t={}){const{document:o=j,autoExit:l=!1}=t,r=n.computed(()=>{var y;return(y=x(e))!=null?y:o?.documentElement}),s=n.shallowRef(!1),i=n.computed(()=>["requestFullscreen","webkitRequestFullscreen","webkitEnterFullscreen","webkitEnterFullScreen","webkitRequestFullScreen","mozRequestFullScreen","msRequestFullscreen"].find(y=>o&&y in o||r.value&&y in r.value)),a=n.computed(()=>["exitFullscreen","webkitExitFullscreen","webkitExitFullScreen","webkitCancelFullScreen","mozCancelFullScreen","msExitFullscreen"].find(y=>o&&y in o||r.value&&y in r.value)),u=n.computed(()=>["fullScreen","webkitIsFullScreen","webkitDisplayingFullscreen","mozFullScreen","msFullscreenElement"].find(y=>o&&y in o||r.value&&y in r.value)),f=["fullscreenElement","webkitFullscreenElement","mozFullScreenElement","msFullscreenElement"].find(y=>o&&y in o),c=W(()=>r.value&&o&&i.value!==void 0&&a.value!==void 0&&u.value!==void 0),d=()=>f?o?.[f]===r.value:!1,h=()=>{if(u.value){if(o&&o[u.value]!=null)return o[u.value];{const y=r.value;if(y?.[u.value]!=null)return!!y[u.value]}}return!1};async function m(){if(!(!c.value||!s.value)){if(a.value)if(o?.[a.value]!=null)await o[a.value]();else{const y=r.value;y?.[a.value]!=null&&await y[a.value]()}s.value=!1}}async function v(){if(!c.value||s.value)return;h()&&await m();const y=r.value;i.value&&y?.[i.value]!=null&&(await y[i.value](),s.value=!0)}async function S(){await(s.value?m():v())}const p=()=>{const y=h();(!y||y&&d())&&(s.value=y)},w={capture:!1,passive:!0};return O(o,Je,p,w),O(()=>x(r),Je,p,w),g.tryOnMounted(p,!1),l&&g.tryOnScopeDispose(m),{isSupported:c,isFullscreen:s,enter:v,exit:m,toggle:S}}function lo(e){return n.computed(()=>e.value?{buttons:{a:e.value.buttons[0],b:e.value.buttons[1],x:e.value.buttons[2],y:e.value.buttons[3]},bumper:{left:e.value.buttons[4],right:e.value.buttons[5]},triggers:{left:e.value.buttons[6],right:e.value.buttons[7]},stick:{left:{horizontal:e.value.axes[0],vertical:e.value.axes[1],button:e.value.buttons[10]},right:{horizontal:e.value.axes[2],vertical:e.value.axes[3],button:e.value.buttons[11]}},dpad:{up:e.value.buttons[12],down:e.value.buttons[13],left:e.value.buttons[14],right:e.value.buttons[15]},back:e.value.buttons[8],start:e.value.buttons[9]}:null)}function ao(e={}){const{navigator:t=z}=e,o=W(()=>t&&"getGamepads"in t),l=n.ref([]),r=g.createEventHook(),s=g.createEventHook(),i=v=>{const S=[],p="vibrationActuator"in v?v.vibrationActuator:null;return p&&S.push(p),v.hapticActuators&&S.push(...v.hapticActuators),{id:v.id,index:v.index,connected:v.connected,mapping:v.mapping,timestamp:v.timestamp,vibrationActuator:v.vibrationActuator,hapticActuators:S,axes:v.axes.map(w=>w),buttons:v.buttons.map(w=>({pressed:w.pressed,touched:w.touched,value:w.value}))}},a=()=>{const v=t?.getGamepads()||[];for(const S of v)S&&l.value[S.index]&&(l.value[S.index]=i(S))},{isActive:u,pause:f,resume:c}=Y(a),d=v=>{l.value.some(({index:S})=>S===v.index)||(l.value.push(i(v)),r.trigger(v.index)),c()},h=v=>{l.value=l.value.filter(S=>S.index!==v.index),s.trigger(v.index)},m={passive:!0};return O("gamepadconnected",v=>d(v.gamepad),m),O("gamepaddisconnected",v=>h(v.gamepad),m),g.tryOnMounted(()=>{const v=t?.getGamepads()||[];for(const S of v)S&&l.value[S.index]&&d(S)}),f(),{isSupported:o,onConnected:r.on,onDisconnected:s.on,gamepads:l,pause:f,resume:c,isActive:u}}function ro(e={}){const{enableHighAccuracy:t=!0,maximumAge:o=3e4,timeout:l=27e3,navigator:r=z,immediate:s=!0}=e,i=W(()=>r&&"geolocation"in r),a=n.shallowRef(null),u=n.shallowRef(null),f=n.ref({accuracy:0,latitude:Number.POSITIVE_INFINITY,longitude:Number.POSITIVE_INFINITY,altitude:null,altitudeAccuracy:null,heading:null,speed:null});function c(v){a.value=v.timestamp,f.value=v.coords,u.value=null}let d;function h(){i.value&&(d=r.geolocation.watchPosition(c,v=>u.value=v,{enableHighAccuracy:t,maximumAge:o,timeout:l}))}s&&h();function m(){d&&r&&r.geolocation.clearWatch(d)}return g.tryOnScopeDispose(()=>{m()}),{isSupported:i,coords:f,locatedAt:a,error:u,resume:h,pause:m}}const io=["mousemove","mousedown","resize","keydown","touchstart","wheel"],so=6e4;function uo(e=so,t={}){const{initialState:o=!1,listenForVisibilityChange:l=!0,events:r=io,window:s=M,eventFilter:i=g.throttleFilter(50)}=t,a=n.shallowRef(o),u=n.shallowRef(g.timestamp());let f;const c=()=>{a.value=!1,clearTimeout(f),f=setTimeout(()=>a.value=!0,e)},d=g.createFilterWrapper(i,()=>{u.value=g.timestamp(),c()});if(s){const h=s.document,m={passive:!0};for(const v of r)O(s,v,d,m);l&&O(h,"visibilitychange",()=>{h.hidden||d()},m),c()}return{idle:a,lastActive:u,reset:c}}async function co(e){return new Promise((t,o)=>{const l=new Image,{src:r,srcset:s,sizes:i,class:a,loading:u,crossorigin:f,referrerPolicy:c,width:d,height:h,decoding:m,fetchPriority:v,ismap:S,usemap:p}=e;l.src=r,s!=null&&(l.srcset=s),i!=null&&(l.sizes=i),a!=null&&(l.className=a),u!=null&&(l.loading=u),f!=null&&(l.crossOrigin=f),c!=null&&(l.referrerPolicy=c),d!=null&&(l.width=d),h!=null&&(l.height=h),m!=null&&(l.decoding=m),v!=null&&(l.fetchPriority=v),S!=null&&(l.isMap=S),p!=null&&(l.useMap=p),l.onload=()=>t(l),l.onerror=o})}function fo(e,t={}){const o=Ie(()=>co(n.toValue(e)),void 0,{resetOnExecute:!0,...t});return n.watch(()=>n.toValue(e),()=>o.execute(t.delay),{deep:!0}),o}function pe(e){return typeof Window<"u"&&e instanceof Window?e.document.documentElement:typeof Document<"u"&&e instanceof Document?e.documentElement:e}const Qe=1;function Oe(e,t={}){const{throttle:o=0,idle:l=200,onStop:r=g.noop,onScroll:s=g.noop,offset:i={left:0,right:0,top:0,bottom:0},eventListenerOptions:a={capture:!1,passive:!0},behavior:u="auto",window:f=M,onError:c=R=>{console.error(R)}}=t,d=n.shallowRef(0),h=n.shallowRef(0),m=n.computed({get(){return d.value},set(R){S(R,void 0)}}),v=n.computed({get(){return h.value},set(R){S(void 0,R)}});function S(R,V){var P,I,L,_;if(!f)return;const D=n.toValue(e);if(!D)return;(L=D instanceof Document?f.document.body:D)==null||L.scrollTo({top:(P=n.toValue(V))!=null?P:v.value,left:(I=n.toValue(R))!=null?I:m.value,behavior:n.toValue(u)});const F=((_=D?.document)==null?void 0:_.documentElement)||D?.documentElement||D;m!=null&&(d.value=F.scrollLeft),v!=null&&(h.value=F.scrollTop)}const p=n.shallowRef(!1),w=n.reactive({left:!0,right:!1,top:!0,bottom:!1}),y=n.reactive({left:!1,right:!1,top:!1,bottom:!1}),b=R=>{p.value&&(p.value=!1,y.left=!1,y.right=!1,y.top=!1,y.bottom=!1,r(R))},T=g.useDebounceFn(b,o+l),k=R=>{var V;if(!f)return;const P=((V=R?.document)==null?void 0:V.documentElement)||R?.documentElement||x(R),{display:I,flexDirection:L,direction:_}=getComputedStyle(P),D=_==="rtl"?-1:1,F=P.scrollLeft;y.left=F<d.value,y.right=F>d.value;const N=Math.abs(F*D)<=(i.left||0),H=Math.abs(F*D)+P.clientWidth>=P.scrollWidth-(i.right||0)-Qe;I==="flex"&&L==="row-reverse"?(w.left=H,w.right=N):(w.left=N,w.right=H),d.value=F;let A=P.scrollTop;R===f.document&&!A&&(A=f.document.body.scrollTop),y.top=A<h.value,y.bottom=A>h.value;const U=Math.abs(A)<=(i.top||0),G=Math.abs(A)+P.clientHeight>=P.scrollHeight-(i.bottom||0)-Qe;I==="flex"&&L==="column-reverse"?(w.top=G,w.bottom=U):(w.top=U,w.bottom=G),h.value=A},C=R=>{var V;if(!f)return;const P=(V=R.target.documentElement)!=null?V:R.target;k(P),p.value=!0,T(R),s(R)};return O(e,"scroll",o?g.useThrottleFn(C,o,!0,!1):C,a),g.tryOnMounted(()=>{try{const R=n.toValue(e);if(!R)return;k(R)}catch(R){c(R)}}),O(e,"scrollend",b,a),{x:m,y:v,isScrolling:p,arrivedState:w,directions:y,measure(){const R=n.toValue(e);f&&R&&k(R)}}}function mo(e,t,o={}){var l;const{direction:r="bottom",interval:s=100,canLoadMore:i=()=>!0}=o,a=n.reactive(Oe(e,{...o,offset:{[r]:(l=o.distance)!=null?l:0,...o.offset}})),u=n.ref(),f=n.computed(()=>!!u.value),c=n.computed(()=>pe(n.toValue(e))),d=Xe(c);function h(){if(a.measure(),!c.value||!d.value||!i(c.value))return;const{scrollHeight:v,clientHeight:S,scrollWidth:p,clientWidth:w}=c.value,y=r==="bottom"||r==="top"?v<=S:p<=w;(a.arrivedState[r]||y)&&(u.value||(u.value=Promise.all([t(a),new Promise(b=>setTimeout(b,s))]).finally(()=>{u.value=null,n.nextTick(()=>h())})))}const m=n.watch(()=>[a.arrivedState[r],d.value],h,{immediate:!0});return g.tryOnUnmounted(m),{isLoading:f,reset(){n.nextTick(()=>h())}}}const vo=["mousedown","mouseup","keydown","keyup"];function po(e,t={}){const{events:o=vo,document:l=j,initial:r=null}=t,s=n.shallowRef(r);return l&&o.forEach(i=>{O(l,i,a=>{typeof a.getModifierState=="function"&&(s.value=a.getModifierState(e))},{passive:!0})}),s}function ho(e,t,o={}){const{window:l=M}=o;return de(e,t,l?.localStorage,o)}const Ze={ctrl:"control",command:"meta",cmd:"meta",option:"alt",up:"arrowup",down:"arrowdown",left:"arrowleft",right:"arrowright"};function yo(e={}){const{reactive:t=!1,target:o=M,aliasMap:l=Ze,passive:r=!0,onEventFired:s=g.noop}=e,i=n.reactive(new Set),a={toJSON(){return{}},current:i},u=t?n.reactive(a):a,f=new Set,c=new Set,d=new Set;function h(p,w){p in u&&(t?u[p]=w:u[p].value=w)}function m(){i.clear();for(const p of d)h(p,!1)}function v(p,w){var y,b;const T=(y=p.key)==null?void 0:y.toLowerCase(),C=[(b=p.code)==null?void 0:b.toLowerCase(),T].filter(Boolean);T&&(w?i.add(T):i.delete(T));for(const R of C)d.add(R),h(R,w);T==="shift"&&!w?(c.forEach(R=>{i.delete(R),h(R,!1)}),c.clear()):typeof p.getModifierState=="function"&&p.getModifierState("Shift")&&w&&[...i,...C].forEach(R=>c.add(R)),T==="meta"&&!w?(f.forEach(R=>{i.delete(R),h(R,!1)}),f.clear()):typeof p.getModifierState=="function"&&p.getModifierState("Meta")&&w&&[...i,...C].forEach(R=>f.add(R))}O(o,"keydown",p=>(v(p,!0),s(p)),{passive:r}),O(o,"keyup",p=>(v(p,!1),s(p)),{passive:r}),O("blur",m,{passive:r}),O("focus",m,{passive:r});const S=new Proxy(u,{get(p,w,y){if(typeof w!="string")return Reflect.get(p,w,y);if(w=w.toLowerCase(),w in l&&(w=l[w]),!(w in u))if(/[+_-]/.test(w)){const T=w.split(/[+_-]/g).map(k=>k.trim());u[w]=n.computed(()=>T.map(k=>n.toValue(S[k])).every(Boolean))}else u[w]=n.shallowRef(!1);const b=Reflect.get(p,w,y);return t?n.toValue(b):b}});return S}function ke(e,t){n.toValue(e)&&t(n.toValue(e))}function wo(e){let t=[];for(let o=0;o<e.length;++o)t=[...t,[e.start(o),e.end(o)]];return t}function _e(e){return Array.from(e).map(({label:t,kind:o,language:l,mode:r,activeCues:s,cues:i,inBandMetadataTrackDispatchType:a},u)=>({id:u,label:t,kind:o,language:l,mode:r,activeCues:s,cues:i,inBandMetadataTrackDispatchType:a}))}const go={src:"",tracks:[]};function bo(e,t={}){e=g.toRef(e),t={...go,...t};const{document:o=j}=t,l={passive:!0},r=n.shallowRef(0),s=n.shallowRef(0),i=n.shallowRef(!1),a=n.shallowRef(1),u=n.shallowRef(!1),f=n.shallowRef(!1),c=n.shallowRef(!1),d=n.shallowRef(1),h=n.shallowRef(!1),m=n.ref([]),v=n.ref([]),S=n.shallowRef(-1),p=n.shallowRef(!1),w=n.shallowRef(!1),y=o&&"pictureInPictureEnabled"in o,b=g.createEventHook(),T=g.createEventHook(),k=_=>{ke(e,D=>{if(_){const F=typeof _=="number"?_:_.id;D.textTracks[F].mode="disabled"}else for(let F=0;F<D.textTracks.length;++F)D.textTracks[F].mode="disabled";S.value=-1})},C=(_,D=!0)=>{ke(e,F=>{const N=typeof _=="number"?_:_.id;D&&k(),F.textTracks[N].mode="showing",S.value=N})},R=()=>new Promise((_,D)=>{ke(e,async F=>{y&&(p.value?o.exitPictureInPicture().then(_).catch(D):F.requestPictureInPicture().then(_).catch(D))})});n.watchEffect(()=>{if(!o)return;const _=n.toValue(e);if(!_)return;const D=n.toValue(t.src);let F=[];D&&(typeof D=="string"?F=[{src:D}]:Array.isArray(D)?F=D:g.isObject(D)&&(F=[D]),_.querySelectorAll("source").forEach(N=>{N.remove()}),F.forEach(({src:N,type:H,media:A})=>{const U=o.createElement("source");U.setAttribute("src",N),U.setAttribute("type",H||""),U.setAttribute("media",A||""),O(U,"error",b.trigger,l),_.appendChild(U)}),_.load())}),n.watch([e,a],()=>{const _=n.toValue(e);_&&(_.volume=a.value)}),n.watch([e,w],()=>{const _=n.toValue(e);_&&(_.muted=w.value)}),n.watch([e,d],()=>{const _=n.toValue(e);_&&(_.playbackRate=d.value)}),n.watchEffect(()=>{if(!o)return;const _=n.toValue(t.tracks),D=n.toValue(e);!_||!_.length||!D||(D.querySelectorAll("track").forEach(F=>F.remove()),_.forEach(({default:F,kind:N,label:H,src:A,srcLang:U},G)=>{const q=o.createElement("track");q.default=F||!1,q.kind=N,q.label=H,q.src=A,q.srclang=U,q.default&&(S.value=G),D.appendChild(q)}))});const{ignoreUpdates:V}=g.watchIgnorable(r,_=>{const D=n.toValue(e);D&&(D.currentTime=_)}),{ignoreUpdates:P}=g.watchIgnorable(c,_=>{const D=n.toValue(e);D&&(_?D.play().catch(F=>{throw T.trigger(F),F}):D.pause())});O(e,"timeupdate",()=>V(()=>r.value=n.toValue(e).currentTime),l),O(e,"durationchange",()=>s.value=n.toValue(e).duration,l),O(e,"progress",()=>m.value=wo(n.toValue(e).buffered),l),O(e,"seeking",()=>i.value=!0,l),O(e,"seeked",()=>i.value=!1,l),O(e,["waiting","loadstart"],()=>{u.value=!0,P(()=>c.value=!1)},l),O(e,"loadeddata",()=>u.value=!1,l),O(e,"playing",()=>{u.value=!1,f.value=!1,P(()=>c.value=!0)},l),O(e,"ratechange",()=>d.value=n.toValue(e).playbackRate,l),O(e,"stalled",()=>h.value=!0,l),O(e,"ended",()=>f.value=!0,l),O(e,"pause",()=>P(()=>c.value=!1),l),O(e,"play",()=>P(()=>c.value=!0),l),O(e,"enterpictureinpicture",()=>p.value=!0,l),O(e,"leavepictureinpicture",()=>p.value=!1,l),O(e,"volumechange",()=>{const _=n.toValue(e);_&&(a.value=_.volume,w.value=_.muted)},l);const I=[],L=n.watch([e],()=>{const _=n.toValue(e);_&&(L(),I[0]=O(_.textTracks,"addtrack",()=>v.value=_e(_.textTracks),l),I[1]=O(_.textTracks,"removetrack",()=>v.value=_e(_.textTracks),l),I[2]=O(_.textTracks,"change",()=>v.value=_e(_.textTracks),l))});return g.tryOnScopeDispose(()=>I.forEach(_=>_())),{currentTime:r,duration:s,waiting:u,seeking:i,ended:f,stalled:h,buffered:m,playing:c,rate:d,volume:a,muted:w,tracks:v,selectedTrack:S,enableTrack:C,disableTrack:k,supportsPictureInPicture:y,togglePictureInPicture:R,isPictureInPicture:p,onSourceError:b.on,onPlaybackError:T.on}}function So(e,t){const l=t?.cache?n.shallowReactive(t.cache):n.shallowReactive(new Map),r=(...c)=>t?.getKey?t.getKey(...c):JSON.stringify(c),s=(c,...d)=>(l.set(c,e(...d)),l.get(c)),i=(...c)=>s(r(...c),...c),a=(...c)=>{l.delete(r(...c))},u=()=>{l.clear()},f=(...c)=>{const d=r(...c);return l.has(d)?l.get(d):s(d,...c)};return f.load=i,f.delete=a,f.clear=u,f.generateKey=r,f.cache=l,f}function Ro(e={}){const t=n.ref(),o=W(()=>typeof performance<"u"&&"memory"in performance);if(o.value){const{interval:l=1e3}=e;g.useIntervalFn(()=>{t.value=performance.memory},l,{immediate:e.immediate,immediateCallback:e.immediateCallback})}return{isSupported:o,memory:t}}const Eo={page:e=>[e.pageX,e.pageY],client:e=>[e.clientX,e.clientY],screen:e=>[e.screenX,e.screenY],movement:e=>e instanceof MouseEvent?[e.movementX,e.movementY]:null};function et(e={}){const{type:t="page",touch:o=!0,resetOnTouchEnds:l=!1,initialValue:r={x:0,y:0},window:s=M,target:i=s,scroll:a=!0,eventFilter:u}=e;let f=null,c=0,d=0;const h=n.shallowRef(r.x),m=n.shallowRef(r.y),v=n.shallowRef(null),S=typeof t=="function"?t:Eo[t],p=R=>{const V=S(R);f=R,V&&([h.value,m.value]=V,v.value="mouse"),s&&(c=s.scrollX,d=s.scrollY)},w=R=>{if(R.touches.length>0){const V=S(R.touches[0]);V&&([h.value,m.value]=V,v.value="touch")}},y=()=>{if(!f||!s)return;const R=S(f);f instanceof MouseEvent&&R&&(h.value=R[0]+s.scrollX-c,m.value=R[1]+s.scrollY-d)},b=()=>{h.value=r.x,m.value=r.y},T=u?R=>u(()=>p(R),{}):R=>p(R),k=u?R=>u(()=>w(R),{}):R=>w(R),C=u?()=>u(()=>y(),{}):()=>y();if(i){const R={passive:!0};O(i,["mousemove","dragover"],T,R),o&&t!=="movement"&&(O(i,["touchstart","touchmove"],k,R),l&&O(i,"touchend",b,R)),a&&t==="page"&&O(s,"scroll",C,R)}return{x:h,y:m,sourceType:v}}function tt(e,t={}){const{handleOutside:o=!0,window:l=M}=t,r=t.type||"page",{x:s,y:i,sourceType:a}=et(t),u=n.shallowRef(e??l?.document.body),f=n.shallowRef(0),c=n.shallowRef(0),d=n.shallowRef(0),h=n.shallowRef(0),m=n.shallowRef(0),v=n.shallowRef(0),S=n.shallowRef(!0);let p=()=>{};return l&&(p=n.watch([u,s,i],()=>{const w=x(u);if(!w||!(w instanceof Element))return;const{left:y,top:b,width:T,height:k}=w.getBoundingClientRect();d.value=y+(r==="page"?l.pageXOffset:0),h.value=b+(r==="page"?l.pageYOffset:0),m.value=k,v.value=T;const C=s.value-d.value,R=i.value-h.value;S.value=T===0||k===0||C<0||R<0||C>T||R>k,(o||!S.value)&&(f.value=C,c.value=R)},{immediate:!0}),O(document,"mouseleave",()=>S.value=!0,{passive:!0})),{x:s,y:i,sourceType:a,elementX:f,elementY:c,elementPositionX:d,elementPositionY:h,elementHeight:m,elementWidth:v,isOutside:S,stop:p}}function To(e={}){const{touch:t=!0,drag:o=!0,capture:l=!1,initialValue:r=!1,window:s=M}=e,i=n.shallowRef(r),a=n.shallowRef(null);if(!s)return{pressed:i,sourceType:a};const u=h=>m=>{var v;i.value=!0,a.value=h,(v=e.onPressed)==null||v.call(e,m)},f=h=>{var m;i.value=!1,a.value=null,(m=e.onReleased)==null||m.call(e,h)},c=n.computed(()=>x(e.target)||s),d={passive:!0,capture:l};return O(c,"mousedown",u("mouse"),d),O(s,"mouseleave",f,d),O(s,"mouseup",f,d),o&&(O(c,"dragstart",u("mouse"),d),O(s,"drop",f,d),O(s,"dragend",f,d)),t&&(O(c,"touchstart",u("touch"),d),O(s,"touchend",f,d),O(s,"touchcancel",f,d)),{pressed:i,sourceType:a}}function Oo(e={}){const{window:t=M}=e,o=t?.navigator,l=W(()=>o&&"language"in o),r=n.shallowRef(o?.language);return O(t,"languagechange",()=>{o&&(r.value=o.language)},{passive:!0}),{isSupported:l,language:r}}function nt(e={}){const{window:t=M}=e,o=t?.navigator,l=W(()=>o&&"connection"in o),r=n.shallowRef(!0),s=n.shallowRef(!1),i=n.shallowRef(void 0),a=n.shallowRef(void 0),u=n.shallowRef(void 0),f=n.shallowRef(void 0),c=n.shallowRef(void 0),d=n.shallowRef(void 0),h=n.shallowRef("unknown"),m=l.value&&o.connection;function v(){o&&(r.value=o.onLine,i.value=r.value?void 0:Date.now(),a.value=r.value?Date.now():void 0,m&&(u.value=m.downlink,f.value=m.downlinkMax,d.value=m.effectiveType,c.value=m.rtt,s.value=m.saveData,h.value=m.type))}const S={passive:!0};return t&&(O(t,"offline",()=>{r.value=!1,i.value=Date.now()},S),O(t,"online",()=>{r.value=!0,a.value=Date.now()},S)),m&&O(m,"change",v,S),v(),{isSupported:l,isOnline:n.readonly(r),saveData:n.readonly(s),offlineAt:n.readonly(i),onlineAt:n.readonly(a),downlink:n.readonly(u),downlinkMax:n.readonly(f),effectiveType:n.readonly(d),rtt:n.readonly(c),type:n.readonly(h)}}function ot(e={}){const{controls:t=!1,interval:o="requestAnimationFrame",immediate:l=!0}=e,r=n.ref(new Date),s=()=>r.value=new Date,i=o==="requestAnimationFrame"?Y(s,{immediate:l}):g.useIntervalFn(s,o,{immediate:l});return t?{now:r,...i}:r}function ko(e){const t=n.shallowRef(),o=()=>{t.value&&URL.revokeObjectURL(t.value),t.value=void 0};return n.watch(()=>n.toValue(e),l=>{o(),l&&(t.value=URL.createObjectURL(l))},{immediate:!0}),g.tryOnScopeDispose(o),n.readonly(t)}function lt(e,t,o){if(typeof e=="function"||n.isReadonly(e))return n.computed(()=>g.clamp(n.toValue(e),n.toValue(t),n.toValue(o)));const l=n.ref(e);return n.computed({get(){return l.value=g.clamp(l.value,n.toValue(t),n.toValue(o))},set(r){l.value=g.clamp(r,n.toValue(t),n.toValue(o))}})}function _o(e){const{total:t=Number.POSITIVE_INFINITY,pageSize:o=10,page:l=1,onPageChange:r=g.noop,onPageSizeChange:s=g.noop,onPageCountChange:i=g.noop}=e,a=lt(o,1,Number.POSITIVE_INFINITY),u=n.computed(()=>Math.max(1,Math.ceil(n.toValue(t)/n.toValue(a)))),f=lt(l,1,u),c=n.computed(()=>f.value===1),d=n.computed(()=>f.value===u.value);n.isRef(l)&&g.syncRef(l,f,{direction:n.isReadonly(l)?"ltr":"both"}),n.isRef(o)&&g.syncRef(o,a,{direction:n.isReadonly(o)?"ltr":"both"});function h(){f.value--}function m(){f.value++}const v={currentPage:f,currentPageSize:a,pageCount:u,isFirstPage:c,isLastPage:d,prev:h,next:m};return n.watch(f,()=>{r(n.reactive(v))}),n.watch(a,()=>{s(n.reactive(v))}),n.watch(u,()=>{i(n.reactive(v))}),v}function Vo(e={}){const{isOnline:t}=nt(e);return t}function Fo(e={}){const{window:t=M}=e,o=n.shallowRef(!1),l=r=>{if(!t)return;r=r||t.event;const s=r.relatedTarget||r.toElement;o.value=!s};if(t){const r={passive:!0};O(t,"mouseout",l,r),O(t.document,"mouseleave",l,r),O(t.document,"mouseenter",l,r)}return o}function at(e={}){const{window:t=M}=e,o=W(()=>t&&"screen"in t&&"orientation"in t.screen),l=o.value?t.screen.orientation:{},r=n.ref(l.type),s=n.shallowRef(l.angle||0);return o.value&&O(t,"orientationchange",()=>{r.value=l.type,s.value=l.angle},{passive:!0}),{isSupported:o,orientation:r,angle:s,lockOrientation:u=>o.value&&typeof l.lock=="function"?l.lock(u):Promise.reject(new Error("Not supported")),unlockOrientation:()=>{o.value&&typeof l.unlock=="function"&&l.unlock()}}}function Po(e,t={}){const{deviceOrientationTiltAdjust:o=p=>p,deviceOrientationRollAdjust:l=p=>p,mouseTiltAdjust:r=p=>p,mouseRollAdjust:s=p=>p,window:i=M}=t,a=n.reactive(ze({window:i})),u=n.reactive(at({window:i})),{elementX:f,elementY:c,elementWidth:d,elementHeight:h}=tt(e,{handleOutside:!1,window:i}),m=n.computed(()=>a.isSupported&&(a.alpha!=null&&a.alpha!==0||a.gamma!=null&&a.gamma!==0)?"deviceOrientation":"mouse"),v=n.computed(()=>{if(m.value==="deviceOrientation"){let p;switch(u.orientation){case"landscape-primary":p=a.gamma/90;break;case"landscape-secondary":p=-a.gamma/90;break;case"portrait-primary":p=-a.beta/90;break;case"portrait-secondary":p=a.beta/90;break;default:p=-a.beta/90}return l(p)}else{const p=-(c.value-h.value/2)/h.value;return s(p)}}),S=n.computed(()=>{if(m.value==="deviceOrientation"){let p;switch(u.orientation){case"landscape-primary":p=a.beta/90;break;case"landscape-secondary":p=-a.beta/90;break;case"portrait-primary":p=a.gamma/90;break;case"portrait-secondary":p=-a.gamma/90;break;default:p=a.gamma/90}return o(p)}else{const p=(f.value-d.value/2)/d.value;return r(p)}});return{roll:v,tilt:S,source:m}}function Co(e=$e()){const t=n.shallowRef(),o=()=>{const l=x(e);l&&(t.value=l.parentElement)};return g.tryOnMounted(o),n.watch(()=>n.toValue(e),o),t}function Do(e,t){const{window:o=M,immediate:l=!0,...r}=e,s=W(()=>o&&"PerformanceObserver"in o);let i;const a=()=>{i?.disconnect()},u=()=>{s.value&&(a(),i=new PerformanceObserver(t),i.observe(r))};return g.tryOnScopeDispose(a),l&&u(),{isSupported:s,start:u,stop:a}}const rt={x:0,y:0,pointerId:0,pressure:0,tiltX:0,tiltY:0,width:0,height:0,twist:0,pointerType:null},Ao=Object.keys(rt);function Mo(e={}){const{target:t=M}=e,o=n.shallowRef(!1),l=n.ref(e.initialValue||{});Object.assign(l.value,rt,l.value);const r=s=>{o.value=!0,!(e.pointerTypes&&!e.pointerTypes.includes(s.pointerType))&&(l.value=g.objectPick(s,Ao,!1))};if(t){const s={passive:!0};O(t,["pointerdown","pointermove","pointerup"],r,s),O(t,"pointerleave",()=>o.value=!1,s)}return{...g.toRefs(l),isInside:o}}function Io(e,t={}){const{document:o=j}=t,l=W(()=>o&&"pointerLockElement"in o),r=n.shallowRef(),s=n.shallowRef();let i;if(l.value){const f={passive:!0};O(o,"pointerlockchange",()=>{var c;const d=(c=o.pointerLockElement)!=null?c:r.value;i&&d===i&&(r.value=o.pointerLockElement,r.value||(i=s.value=null))},f),O(o,"pointerlockerror",()=>{var c;const d=(c=o.pointerLockElement)!=null?c:r.value;if(i&&d===i){const h=o.pointerLockElement?"release":"acquire";throw new Error(`Failed to ${h} pointer lock.`)}},f)}async function a(f){var c;if(!l.value)throw new Error("Pointer Lock API is not supported by your browser.");if(s.value=f instanceof Event?f.currentTarget:null,i=f instanceof Event?(c=x(e))!=null?c:s.value:x(f),!i)throw new Error("Target element undefined.");return i.requestPointerLock(),await g.until(r).toBe(i)}async function u(){return r.value?(o.exitPointerLock(),await g.until(r).toBeNull(),!0):!1}return{isSupported:l,element:r,triggerElement:s,lock:a,unlock:u}}function Lo(e,t={}){const o=g.toRef(e),{threshold:l=50,onSwipe:r,onSwipeEnd:s,onSwipeStart:i,disableTextSelect:a=!1}=t,u=n.reactive({x:0,y:0}),f=(V,P)=>{u.x=V,u.y=P},c=n.reactive({x:0,y:0}),d=(V,P)=>{c.x=V,c.y=P},h=n.computed(()=>u.x-c.x),m=n.computed(()=>u.y-c.y),{max:v,abs:S}=Math,p=n.computed(()=>v(S(h.value),S(m.value))>=l),w=n.shallowRef(!1),y=n.shallowRef(!1),b=n.computed(()=>p.value?S(h.value)>S(m.value)?h.value>0?"left":"right":m.value>0?"up":"down":"none"),T=V=>{var P,I,L;const _=V.buttons===0,D=V.buttons===1;return(L=(I=(P=t.pointerTypes)==null?void 0:P.includes(V.pointerType))!=null?I:_||D)!=null?L:!0},k={passive:!0},C=[O(e,"pointerdown",V=>{if(!T(V))return;y.value=!0;const P=V.target;P?.setPointerCapture(V.pointerId);const{clientX:I,clientY:L}=V;f(I,L),d(I,L),i?.(V)},k),O(e,"pointermove",V=>{if(!T(V)||!y.value)return;const{clientX:P,clientY:I}=V;d(P,I),!w.value&&p.value&&(w.value=!0),w.value&&r?.(V)},k),O(e,"pointerup",V=>{T(V)&&(w.value&&s?.(V,b.value),y.value=!1,w.value=!1)},k)];g.tryOnMounted(()=>{var V,P,I,L,_,D,F,N;(P=(V=o.value)==null?void 0:V.style)==null||P.setProperty("touch-action","pan-y"),a&&((L=(I=o.value)==null?void 0:I.style)==null||L.setProperty("-webkit-user-select","none"),(D=(_=o.value)==null?void 0:_.style)==null||D.setProperty("-ms-user-select","none"),(N=(F=o.value)==null?void 0:F.style)==null||N.setProperty("user-select","none"))});const R=()=>C.forEach(V=>V());return{isSwiping:n.readonly(w),direction:n.readonly(b),posStart:n.readonly(u),posEnd:n.readonly(c),distanceX:h,distanceY:m,stop:R}}function No(e){const t=$("(prefers-color-scheme: light)",e),o=$("(prefers-color-scheme: dark)",e);return n.computed(()=>o.value?"dark":t.value?"light":"no-preference")}function xo(e){const t=$("(prefers-contrast: more)",e),o=$("(prefers-contrast: less)",e),l=$("(prefers-contrast: custom)",e);return n.computed(()=>t.value?"more":o.value?"less":l.value?"custom":"no-preference")}function Wo(e={}){const{window:t=M}=e;if(!t)return n.ref(["en"]);const o=t.navigator,l=n.ref(o.languages);return O(t,"languagechange",()=>{l.value=o.languages},{passive:!0}),l}function Ho(e){const t=$("(prefers-reduced-motion: reduce)",e);return n.computed(()=>t.value?"reduce":"no-preference")}function Uo(e){const t=$("(prefers-reduced-transparency: reduce)",e);return n.computed(()=>t.value?"reduce":"no-preference")}function $o(e,t){const o=n.shallowRef(t);return n.watch(g.toRef(e),(l,r)=>{o.value=r},{flush:"sync"}),n.readonly(o)}const it="--vueuse-safe-area-top",st="--vueuse-safe-area-right",ut="--vueuse-safe-area-bottom",ct="--vueuse-safe-area-left";function Bo(){const e=n.shallowRef(""),t=n.shallowRef(""),o=n.shallowRef(""),l=n.shallowRef("");if(g.isClient){const s=oe(it),i=oe(st),a=oe(ut),u=oe(ct);s.value="env(safe-area-inset-top, 0px)",i.value="env(safe-area-inset-right, 0px)",a.value="env(safe-area-inset-bottom, 0px)",u.value="env(safe-area-inset-left, 0px)",r(),O("resize",g.useDebounceFn(r),{passive:!0})}function r(){e.value=he(it),t.value=he(st),o.value=he(ut),l.value=he(ct)}return{top:e,right:t,bottom:o,left:l,update:r}}function he(e){return getComputedStyle(document.documentElement).getPropertyValue(e)}function jo(e,t=g.noop,o={}){const{immediate:l=!0,manual:r=!1,type:s="text/javascript",async:i=!0,crossOrigin:a,referrerPolicy:u,noModule:f,defer:c,document:d=j,attrs:h={}}=o,m=n.shallowRef(null);let v=null;const S=y=>new Promise((b,T)=>{const k=P=>(m.value=P,b(P),P);if(!d){b(!1);return}let C=!1,R=d.querySelector(`script[src="${n.toValue(e)}"]`);R?R.hasAttribute("data-loaded")&&k(R):(R=d.createElement("script"),R.type=s,R.async=i,R.src=n.toValue(e),c&&(R.defer=c),a&&(R.crossOrigin=a),f&&(R.noModule=f),u&&(R.referrerPolicy=u),Object.entries(h).forEach(([P,I])=>R?.setAttribute(P,I)),C=!0);const V={passive:!0};O(R,"error",P=>T(P),V),O(R,"abort",P=>T(P),V),O(R,"load",()=>{R.setAttribute("data-loaded","true"),t(R),k(R)},V),C&&(R=d.head.appendChild(R)),y||k(R)}),p=(y=!0)=>(v||(v=S(y)),v),w=()=>{if(!d)return;v=null,m.value&&(m.value=null);const y=d.querySelector(`script[src="${n.toValue(e)}"]`);y&&d.head.removeChild(y)};return l&&!r&&g.tryOnMounted(p),r||g.tryOnUnmounted(w),{scriptTag:m,load:p,unload:w}}function ft(e){const t=window.getComputedStyle(e);if(t.overflowX==="scroll"||t.overflowY==="scroll"||t.overflowX==="auto"&&e.clientWidth<e.scrollWidth||t.overflowY==="auto"&&e.clientHeight<e.scrollHeight)return!0;{const o=e.parentNode;return!o||o.tagName==="BODY"?!1:ft(o)}}function zo(e){const t=e||window.event,o=t.target;return ft(o)?!1:t.touches.length>1?!0:(t.preventDefault&&t.preventDefault(),!1)}const Ve=new WeakMap;function qo(e,t=!1){const o=n.shallowRef(t);let l=null,r="";n.watch(g.toRef(e),a=>{const u=pe(n.toValue(a));if(u){const f=u;if(Ve.get(f)||Ve.set(f,f.style.overflow),f.style.overflow!=="hidden"&&(r=f.style.overflow),f.style.overflow==="hidden")return o.value=!0;if(o.value)return f.style.overflow="hidden"}},{immediate:!0});const s=()=>{const a=pe(n.toValue(e));!a||o.value||(g.isIOS&&(l=O(a,"touchmove",u=>{zo(u)},{passive:!1})),a.style.overflow="hidden",o.value=!0)},i=()=>{const a=pe(n.toValue(e));!a||!o.value||(g.isIOS&&l?.(),a.style.overflow=r,Ve.delete(a),o.value=!1)};return g.tryOnScopeDispose(i),n.computed({get(){return o.value},set(a){a?s():i()}})}function Go(e,t,o={}){const{window:l=M}=o;return de(e,t,l?.sessionStorage,o)}function Yo(e={},t={}){const{navigator:o=z}=t,l=o,r=W(()=>l&&"canShare"in l);return{isSupported:r,share:async(i={})=>{if(r.value){const a={...n.toValue(e),...n.toValue(i)};let u=!0;if(a.files&&l.canShare&&(u=l.canShare({files:a.files})),u)return l.share(a)}}}}const Xo=(e,t)=>e.sort(t),ye=(e,t)=>e-t;function Ko(...e){var t,o,l,r;const[s]=e;let i=ye,a={};e.length===2?typeof e[1]=="object"?(a=e[1],i=(t=a.compareFn)!=null?t:ye):i=(o=e[1])!=null?o:ye:e.length>2&&(i=(l=e[1])!=null?l:ye,a=(r=e[2])!=null?r:{});const{dirty:u=!1,sortFn:f=Xo}=a;return u?(n.watchEffect(()=>{const c=f(n.toValue(s),i);n.isRef(s)?s.value=c:s.splice(0,s.length,...c)}),s):n.computed(()=>f([...n.toValue(s)],i))}function Jo(e={}){const{interimResults:t=!0,continuous:o=!0,maxAlternatives:l=1,window:r=M}=e,s=g.toRef(e.lang||"en-US"),i=n.shallowRef(!1),a=n.shallowRef(!1),u=n.shallowRef(""),f=n.shallowRef(void 0);let c;const d=()=>{i.value=!0},h=()=>{i.value=!1},m=(p=!i.value)=>{p?d():h()},v=r&&(r.SpeechRecognition||r.webkitSpeechRecognition),S=W(()=>v);return S.value&&(c=new v,c.continuous=o,c.interimResults=t,c.lang=n.toValue(s),c.maxAlternatives=l,c.onstart=()=>{i.value=!0,a.value=!1},n.watch(s,p=>{c&&!i.value&&(c.lang=p)}),c.onresult=p=>{const w=p.results[p.resultIndex],{transcript:y}=w[0];a.value=w.isFinal,u.value=y,f.value=void 0},c.onerror=p=>{f.value=p},c.onend=()=>{i.value=!1,c.lang=n.toValue(s)},n.watch(i,(p,w)=>{p!==w&&(p?c.start():c.stop())})),g.tryOnScopeDispose(()=>{h()}),{isSupported:S,isListening:i,isFinal:a,recognition:c,result:u,error:f,toggle:m,start:d,stop:h}}function Qo(e,t={}){const{pitch:o=1,rate:l=1,volume:r=1,window:s=M}=t,i=s&&s.speechSynthesis,a=W(()=>i),u=n.shallowRef(!1),f=n.shallowRef("init"),c=g.toRef(e||""),d=g.toRef(t.lang||"en-US"),h=n.shallowRef(void 0),m=(y=!u.value)=>{u.value=y},v=y=>{y.lang=n.toValue(d),y.voice=n.toValue(t.voice)||null,y.pitch=n.toValue(o),y.rate=n.toValue(l),y.volume=r,y.onstart=()=>{u.value=!0,f.value="play"},y.onpause=()=>{u.value=!1,f.value="pause"},y.onresume=()=>{u.value=!0,f.value="play"},y.onend=()=>{u.value=!1,f.value="end"},y.onerror=b=>{h.value=b}},S=n.computed(()=>{u.value=!1,f.value="init";const y=new SpeechSynthesisUtterance(c.value);return v(y),y}),p=()=>{i.cancel(),S&&i.speak(S.value)},w=()=>{i.cancel(),u.value=!1};return a.value&&(v(S.value),n.watch(d,y=>{S.value&&!u.value&&(S.value.lang=y)}),t.voice&&n.watch(t.voice,()=>{i.cancel()}),n.watch(u,()=>{u.value?i.resume():i.pause()})),g.tryOnScopeDispose(()=>{u.value=!1}),{isSupported:a,isPlaying:u,status:f,utterance:S,error:h,stop:w,toggle:m,speak:p}}function Zo(e,t){const o=n.ref(e),l=n.computed(()=>Array.isArray(o.value)?o.value:Object.keys(o.value)),r=n.ref(l.value.indexOf(t??l.value[0])),s=n.computed(()=>c(r.value)),i=n.computed(()=>r.value===0),a=n.computed(()=>r.value===l.value.length-1),u=n.computed(()=>l.value[r.value+1]),f=n.computed(()=>l.value[r.value-1]);function c(k){return Array.isArray(o.value)?o.value[k]:o.value[l.value[k]]}function d(k){if(l.value.includes(k))return c(l.value.indexOf(k))}function h(k){l.value.includes(k)&&(r.value=l.value.indexOf(k))}function m(){a.value||r.value++}function v(){i.value||r.value--}function S(k){T(k)&&h(k)}function p(k){return l.value.indexOf(k)===r.value+1}function w(k){return l.value.indexOf(k)===r.value-1}function y(k){return l.value.indexOf(k)===r.value}function b(k){return r.value<l.value.indexOf(k)}function T(k){return r.value>l.value.indexOf(k)}return{steps:o,stepNames:l,index:r,current:s,next:u,previous:f,isFirst:i,isLast:a,at:c,get:d,goTo:h,goToNext:m,goToPrevious:v,goBackTo:S,isNext:p,isPrevious:w,isCurrent:y,isBefore:b,isAfter:T}}function el(e,t,o,l={}){var r;const{flush:s="pre",deep:i=!0,listenToStorageChanges:a=!0,writeDefaults:u=!0,mergeDefaults:f=!1,shallow:c,window:d=M,eventFilter:h,onError:m=b=>{console.error(b)}}=l,v=n.toValue(t),S=He(v),p=(c?n.shallowRef:n.ref)(n.toValue(t)),w=(r=l.serializer)!=null?r:Re[S];if(!o)try{o=fe("getDefaultStorageAsync",()=>{var b;return(b=M)==null?void 0:b.localStorage})()}catch(b){m(b)}async function y(b){if(!(!o||b&&b.key!==e))try{const T=b?b.newValue:await o.getItem(e);if(T==null)p.value=v,u&&v!==null&&await o.setItem(e,await w.write(v));else if(f){const k=await w.read(T);typeof f=="function"?p.value=f(k,v):S==="object"&&!Array.isArray(k)?p.value={...v,...k}:p.value=k}else p.value=await w.read(T)}catch(T){m(T)}}return y(),d&&a&&O(d,"storage",b=>Promise.resolve().then(()=>y(b)),{passive:!0}),o&&g.watchWithFilter(p,async()=>{try{p.value==null?await o.removeItem(e):await o.setItem(e,await w.write(p.value))}catch(b){m(b)}},{flush:s,deep:i,eventFilter:h}),p}let tl=0;function nl(e,t={}){const o=n.shallowRef(!1),{document:l=j,immediate:r=!0,manual:s=!1,id:i=`vueuse_styletag_${++tl}`}=t,a=n.shallowRef(e);let u=()=>{};const f=()=>{if(!l)return;const d=l.getElementById(i)||l.createElement("style");d.isConnected||(d.id=i,t.nonce&&(d.nonce=t.nonce),t.media&&(d.media=t.media),l.head.appendChild(d)),!o.value&&(u=n.watch(a,h=>{d.textContent=h},{immediate:!0}),o.value=!0)},c=()=>{!l||!o.value||(u(),l.head.removeChild(l.getElementById(i)),o.value=!1)};return r&&!s&&g.tryOnMounted(f),s||g.tryOnScopeDispose(c),{id:i,css:a,unload:c,load:f,isLoaded:n.readonly(o)}}function ol(e,t={}){const{threshold:o=50,onSwipe:l,onSwipeEnd:r,onSwipeStart:s,passive:i=!0}=t,a=n.reactive({x:0,y:0}),u=n.reactive({x:0,y:0}),f=n.computed(()=>a.x-u.x),c=n.computed(()=>a.y-u.y),{max:d,abs:h}=Math,m=n.computed(()=>d(h(f.value),h(c.value))>=o),v=n.shallowRef(!1),S=n.computed(()=>m.value?h(f.value)>h(c.value)?f.value>0?"left":"right":c.value>0?"up":"down":"none"),p=R=>[R.touches[0].clientX,R.touches[0].clientY],w=(R,V)=>{a.x=R,a.y=V},y=(R,V)=>{u.x=R,u.y=V},b={passive:i,capture:!i},T=R=>{v.value&&r?.(R,S.value),v.value=!1},k=[O(e,"touchstart",R=>{if(R.touches.length!==1)return;const[V,P]=p(R);w(V,P),y(V,P),s?.(R)},b),O(e,"touchmove",R=>{if(R.touches.length!==1)return;const[V,P]=p(R);y(V,P),b.capture&&!b.passive&&Math.abs(f.value)>Math.abs(c.value)&&R.preventDefault(),!v.value&&m.value&&(v.value=!0),v.value&&l?.(R)},b),O(e,["touchend","touchcancel"],T,b)];return{isSwiping:v,direction:S,coordsStart:a,coordsEnd:u,lengthX:f,lengthY:c,stop:()=>k.forEach(R=>R()),isPassiveEventSupported:!0}}function ll(){const e=n.ref([]);return e.value.set=t=>{t&&e.value.push(t)},n.onBeforeUpdate(()=>{e.value.length=0}),e}function al(e={}){const{document:t=j,selector:o="html",observe:l=!1,initialValue:r="ltr"}=e;function s(){var a,u;return(u=(a=t?.querySelector(o))==null?void 0:a.getAttribute("dir"))!=null?u:r}const i=n.ref(s());return g.tryOnMounted(()=>i.value=s()),l&&t&&Q(t.querySelector(o),()=>i.value=s(),{attributes:!0}),n.computed({get(){return i.value},set(a){var u,f;i.value=a,t&&(i.value?(u=t.querySelector(o))==null||u.setAttribute("dir",i.value):(f=t.querySelector(o))==null||f.removeAttribute("dir"))}})}function rl(e){var t;const o=(t=e.rangeCount)!=null?t:0;return Array.from({length:o},(l,r)=>e.getRangeAt(r))}function il(e={}){const{window:t=M}=e,o=n.ref(null),l=n.computed(()=>{var a,u;return(u=(a=o.value)==null?void 0:a.toString())!=null?u:""}),r=n.computed(()=>o.value?rl(o.value):[]),s=n.computed(()=>r.value.map(a=>a.getBoundingClientRect()));function i(){o.value=null,t&&(o.value=t.getSelection())}return t&&O(t.document,"selectionchange",i,{passive:!0}),{text:l,rects:s,ranges:r,selection:o}}function sl(e=M,t){e&&typeof e.requestAnimationFrame=="function"?e.requestAnimationFrame(t):t()}function ul(e={}){var t,o;const{window:l=M}=e,r=g.toRef(e?.element),s=g.toRef((t=e?.input)!=null?t:""),i=(o=e?.styleProp)!=null?o:"height",a=n.shallowRef(1),u=n.shallowRef(0);function f(){var c;if(!r.value)return;let d="";r.value.style[i]="1px",a.value=(c=r.value)==null?void 0:c.scrollHeight;const h=n.toValue(e?.styleTarget);h?h.style[i]=`${a.value}px`:d=`${a.value}px`,r.value.style[i]=d}return n.watch([s,r],()=>n.nextTick(f),{immediate:!0}),n.watch(a,()=>{var c;return(c=e?.onResize)==null?void 0:c.call(e)}),me(r,([{contentRect:c}])=>{u.value!==c.width&&sl(l,()=>{u.value=c.width,f()})}),e?.watch&&n.watch(e.watch,f,{immediate:!0,deep:!0}),{textarea:r,input:s,triggerResize:f}}function cl(e,t={}){const{throttle:o=200,trailing:l=!0}=t,r=g.throttleFilter(o,l);return{...Te(e,{...t,eventFilter:r})}}const fl=[{max:6e4,value:1e3,name:"second"},{max:276e4,value:6e4,name:"minute"},{max:72e6,value:36e5,name:"hour"},{max:5184e5,value:864e5,name:"day"},{max:24192e5,value:6048e5,name:"week"},{max:28512e6,value:2592e6,name:"month"},{max:Number.POSITIVE_INFINITY,value:31536e6,name:"year"}],dl={justNow:"just now",past:e=>e.match(/\d/)?`${e} ago`:e,future:e=>e.match(/\d/)?`in ${e}`:e,month:(e,t)=>e===1?t?"last month":"next month":`${e} month${e>1?"s":""}`,year:(e,t)=>e===1?t?"last year":"next year":`${e} year${e>1?"s":""}`,day:(e,t)=>e===1?t?"yesterday":"tomorrow":`${e} day${e>1?"s":""}`,week:(e,t)=>e===1?t?"last week":"next week":`${e} week${e>1?"s":""}`,hour:e=>`${e} hour${e>1?"s":""}`,minute:e=>`${e} minute${e>1?"s":""}`,second:e=>`${e} second${e>1?"s":""}`,invalid:""};function ml(e){return e.toISOString().slice(0,10)}function vl(e,t={}){const{controls:o=!1,updateInterval:l=3e4}=t,{now:r,...s}=ot({interval:l,controls:!0}),i=n.computed(()=>dt(new Date(n.toValue(e)),t,n.toValue(r)));return o?{timeAgo:i,...s}:i}function dt(e,t={},o=Date.now()){var l;const{max:r,messages:s=dl,fullDateFormatter:i=ml,units:a=fl,showSecond:u=!1,rounding:f="round"}=t,c=typeof f=="number"?p=>+p.toFixed(f):Math[f],d=+o-+e,h=Math.abs(d);function m(p,w){return c(Math.abs(p)/w.value)}function v(p,w){const y=m(p,w),b=p>0,T=S(w.name,y,b);return S(b?"past":"future",T,b)}function S(p,w,y){const b=s[p];return typeof b=="function"?b(w,y):b.replace("{0}",w.toString())}if(h<6e4&&!u)return s.justNow;if(typeof r=="number"&&h>r)return i(new Date(e));if(typeof r=="string"){const p=(l=a.find(w=>w.name===r))==null?void 0:l.max;if(p&&h>p)return i(new Date(e))}for(const[p,w]of a.entries()){if(m(d,w)<=0&&a[p-1])return v(d,a[p-1]);if(h<w.max)return v(d,w)}return s.invalid}function pl(e,t,o={}){const{immediate:l=!0,immediateCallback:r=!1}=o,{start:s}=g.useTimeoutFn(a,t,{immediate:l}),i=n.shallowRef(!1);async function a(){i.value&&(await e(),s())}function u(){i.value||(i.value=!0,r&&e(),s())}function f(){i.value=!1}return l&&g.isClient&&u(),g.tryOnScopeDispose(f),{isActive:i,pause:f,resume:u}}function hl(e={}){const{controls:t=!1,offset:o=0,immediate:l=!0,interval:r="requestAnimationFrame",callback:s}=e,i=n.shallowRef(g.timestamp()+o),a=()=>i.value=g.timestamp()+o,u=s?()=>{a(),s(i.value)}:a,f=r==="requestAnimationFrame"?Y(u,{immediate:l}):g.useIntervalFn(u,r,{immediate:l});return t?{timestamp:i,...f}:i}function yl(e=null,t={}){var o,l,r;const{document:s=j,restoreOnUnmount:i=d=>d}=t,a=(o=s?.title)!=null?o:"",u=g.toRef((l=e??s?.title)!=null?l:null),f=!!(e&&typeof e=="function");function c(d){if(!("titleTemplate"in t))return d;const h=t.titleTemplate||"%s";return typeof h=="function"?h(d):n.toValue(h).replace(/%s/g,d)}return n.watch(u,(d,h)=>{d!==h&&s&&(s.title=c(d??""))},{immediate:!0}),t.observe&&!t.titleTemplate&&s&&!f&&Q((r=s.head)==null?void 0:r.querySelector("title"),()=>{s&&s.title!==u.value&&(u.value=c(s.title))},{childList:!0}),g.tryOnScopeDispose(()=>{if(i){const d=i(a,u.value||"");d!=null&&s&&(s.title=d)}}),u}const wl={easeInSine:[.12,0,.39,0],easeOutSine:[.61,1,.88,1],easeInOutSine:[.37,0,.63,1],easeInQuad:[.11,0,.5,0],easeOutQuad:[.5,1,.89,1],easeInOutQuad:[.45,0,.55,1],easeInCubic:[.32,0,.67,0],easeOutCubic:[.33,1,.68,1],easeInOutCubic:[.65,0,.35,1],easeInQuart:[.5,0,.75,0],easeOutQuart:[.25,1,.5,1],easeInOutQuart:[.76,0,.24,1],easeInQuint:[.64,0,.78,0],easeOutQuint:[.22,1,.36,1],easeInOutQuint:[.83,0,.17,1],easeInExpo:[.7,0,.84,0],easeOutExpo:[.16,1,.3,1],easeInOutExpo:[.87,0,.13,1],easeInCirc:[.55,0,1,.45],easeOutCirc:[0,.55,.45,1],easeInOutCirc:[.85,0,.15,1],easeInBack:[.36,0,.66,-.56],easeOutBack:[.34,1.56,.64,1],easeInOutBack:[.68,-.6,.32,1.6]},gl=Object.assign({},{linear:g.identity},wl);function bl([e,t,o,l]){const r=(c,d)=>1-3*d+3*c,s=(c,d)=>3*d-6*c,i=c=>3*c,a=(c,d,h)=>((r(d,h)*c+s(d,h))*c+i(d))*c,u=(c,d,h)=>3*r(d,h)*c*c+2*s(d,h)*c+i(d),f=c=>{let d=c;for(let h=0;h<4;++h){const m=u(d,e,o);if(m===0)return d;const v=a(d,e,o)-c;d-=v/m}return d};return c=>e===t&&o===l?c:a(f(c),t,l)}function mt(e,t,o){return e+o*(t-e)}function Fe(e){return(typeof e=="number"?[e]:e)||[]}function vt(e,t,o,l={}){var r,s;const i=n.toValue(t),a=n.toValue(o),u=Fe(i),f=Fe(a),c=(r=n.toValue(l.duration))!=null?r:1e3,d=Date.now(),h=Date.now()+c,m=typeof l.transition=="function"?l.transition:(s=n.toValue(l.transition))!=null?s:g.identity,v=typeof m=="function"?m:bl(m);return new Promise(S=>{e.value=i;const p=()=>{var w;if((w=l.abort)!=null&&w.call(l)){S();return}const y=Date.now(),b=v((y-d)/c),T=Fe(e.value).map((k,C)=>mt(u[C],f[C],b));Array.isArray(e.value)?e.value=T.map((k,C)=>{var R,V;return mt((R=u[C])!=null?R:0,(V=f[C])!=null?V:0,b)}):typeof e.value=="number"&&(e.value=T[0]),y<h?requestAnimationFrame(p):(e.value=a,S())};p()})}function Sl(e,t={}){let o=0;const l=()=>{const s=n.toValue(e);return typeof s=="number"?s:s.map(n.toValue)},r=n.ref(l());return n.watch(l,async s=>{var i,a;if(n.toValue(t.disabled))return;const u=++o;if(t.delay&&await g.promiseTimeout(n.toValue(t.delay)),u!==o)return;const f=Array.isArray(s)?s.map(n.toValue):n.toValue(s);(i=t.onStarted)==null||i.call(t),await vt(r,r.value,f,{...t,abort:()=>{var c;return u!==o||((c=t.abort)==null?void 0:c.call(t))}}),(a=t.onFinished)==null||a.call(t)},{deep:!0}),n.watch(()=>n.toValue(t.disabled),s=>{s&&(o++,r.value=l())}),g.tryOnScopeDispose(()=>{o++}),n.computed(()=>n.toValue(t.disabled)?l():r.value)}function Rl(e="history",t={}){const{initialValue:o={},removeNullishValues:l=!0,removeFalsyValues:r=!1,write:s=!0,writeMode:i="replace",window:a=M}=t;if(!a)return n.reactive(o);const u=n.reactive({});function f(){if(e==="history")return a.location.search||"";if(e==="hash"){const b=a.location.hash||"",T=b.indexOf("?");return T>0?b.slice(T):""}else return(a.location.hash||"").replace(/^#/,"")}function c(b){const T=b.toString();if(e==="history")return`${T?`?${T}`:""}${a.location.hash||""}`;if(e==="hash-params")return`${a.location.search||""}${T?`#${T}`:""}`;const k=a.location.hash||"#",C=k.indexOf("?");return C>0?`${a.location.search||""}${k.slice(0,C)}${T?`?${T}`:""}`:`${a.location.search||""}${k}${T?`?${T}`:""}`}function d(){return new URLSearchParams(f())}function h(b){const T=new Set(Object.keys(u));for(const k of b.keys()){const C=b.getAll(k);u[k]=C.length>1?C:b.get(k)||"",T.delete(k)}Array.from(T).forEach(k=>delete u[k])}const{pause:m,resume:v}=g.pausableWatch(u,()=>{const b=new URLSearchParams("");Object.keys(u).forEach(T=>{const k=u[T];Array.isArray(k)?k.forEach(C=>b.append(T,C)):l&&k==null||r&&!k?b.delete(T):b.set(T,k)}),S(b,!1)},{deep:!0});function S(b,T){m(),T&&h(b),i==="replace"?a.history.replaceState(a.history.state,a.document.title,a.location.pathname+c(b)):a.history.pushState(a.history.state,a.document.title,a.location.pathname+c(b)),v()}function p(){s&&S(d(),!0)}const w={passive:!0};O(a,"popstate",p,w),e!=="history"&&O(a,"hashchange",p,w);const y=d();return y.keys().next().value?h(y):Object.assign(u,o),u}function El(e={}){var t,o;const l=n.shallowRef((t=e.enabled)!=null?t:!1),r=n.shallowRef((o=e.autoSwitch)!=null?o:!0),s=n.ref(e.constraints),{navigator:i=z}=e,a=W(()=>{var S;return(S=i?.mediaDevices)==null?void 0:S.getUserMedia}),u=n.shallowRef();function f(S){switch(S){case"video":{if(s.value)return s.value.video||!1;break}case"audio":{if(s.value)return s.value.audio||!1;break}}}async function c(){if(!(!a.value||u.value))return u.value=await i.mediaDevices.getUserMedia({video:f("video"),audio:f("audio")}),u.value}function d(){var S;(S=u.value)==null||S.getTracks().forEach(p=>p.stop()),u.value=void 0}function h(){d(),l.value=!1}async function m(){return await c(),u.value&&(l.value=!0),u.value}async function v(){return d(),await m()}return n.watch(l,S=>{S?c():d()},{immediate:!0}),n.watch(s,()=>{r.value&&u.value&&v()},{immediate:!0}),g.tryOnScopeDispose(()=>{h()}),{isSupported:a,stream:u,start:m,stop:h,restart:v,constraints:s,enabled:l,autoSwitch:r}}function pt(e,t,o,l={}){var r,s,i;const{clone:a=!1,passive:u=!1,eventName:f,deep:c=!1,defaultValue:d,shouldEmit:h}=l,m=n.getCurrentInstance(),v=o||m?.emit||((r=m?.$emit)==null?void 0:r.bind(m))||((i=(s=m?.proxy)==null?void 0:s.$emit)==null?void 0:i.bind(m?.proxy));let S=f;t||(t="modelValue"),S=S||`update:${t.toString()}`;const p=b=>a?typeof a=="function"?a(b):ne(b):b,w=()=>g.isDef(e[t])?p(e[t]):d,y=b=>{h?h(b)&&v(S,b):v(S,b)};if(u){const b=w(),T=n.ref(b);let k=!1;return n.watch(()=>e[t],C=>{k||(k=!0,T.value=p(C),n.nextTick(()=>k=!1))}),n.watch(T,C=>{!k&&(C!==e[t]||c)&&y(C)},{deep:c}),T}else return n.computed({get(){return w()},set(b){y(b)}})}function Tl(e,t,o={}){const l={};for(const r in e)l[r]=pt(e,r,t,o);return l}function Ol(e){const{pattern:t=[],interval:o=0,navigator:l=z}=e||{},r=W(()=>typeof l<"u"&&"vibrate"in l),s=g.toRef(t);let i;const a=(f=s.value)=>{r.value&&l.vibrate(f)},u=()=>{r.value&&l.vibrate(0),i?.pause()};return o>0&&(i=g.useIntervalFn(a,o,{immediate:!1,immediateCallback:!1})),{isSupported:r,pattern:t,intervalControls:i,vibrate:a,stop:u}}function kl(e,t){const{containerStyle:o,wrapperProps:l,scrollTo:r,calculateRange:s,currentList:i,containerRef:a}="itemHeight"in t?Fl(t,e):Vl(t,e);return{list:i,scrollTo:r,containerProps:{ref:a,onScroll:()=>{s()},style:o},wrapperProps:l}}function ht(e){const t=n.shallowRef(null),o=Ge(t),l=n.ref([]),r=n.shallowRef(e);return{state:n.ref({start:0,end:10}),source:r,currentList:l,size:o,containerRef:t}}function yt(e,t,o){return l=>{if(typeof o=="number")return Math.ceil(l/o);const{start:r=0}=e.value;let s=0,i=0;for(let a=r;a<t.value.length;a++){const u=o(a);if(s+=u,i=a,s>l)break}return i-r}}function wt(e,t){return o=>{if(typeof t=="number")return Math.floor(o/t)+1;let l=0,r=0;for(let s=0;s<e.value.length;s++){const i=t(s);if(l+=i,l>=o){r=s;break}}return r+1}}function gt(e,t,o,l,{containerRef:r,state:s,currentList:i,source:a}){return()=>{const u=r.value;if(u){const f=o(e==="vertical"?u.scrollTop:u.scrollLeft),c=l(e==="vertical"?u.clientHeight:u.clientWidth),d=f-t,h=f+c+t;s.value={start:d<0?0:d,end:h>a.value.length?a.value.length:h},i.value=a.value.slice(s.value.start,s.value.end).map((m,v)=>({data:m,index:v+s.value.start}))}}}function bt(e,t){return o=>typeof e=="number"?o*e:t.value.slice(0,o).reduce((r,s,i)=>r+e(i),0)}function St(e,t,o,l){n.watch([e.width,e.height,t,o],()=>{l()})}function Rt(e,t){return n.computed(()=>typeof e=="number"?t.value.length*e:t.value.reduce((o,l,r)=>o+e(r),0))}const _l={horizontal:"scrollLeft",vertical:"scrollTop"};function Et(e,t,o,l){return r=>{l.value&&(l.value[_l[e]]=o(r),t())}}function Vl(e,t){const o=ht(t),{state:l,source:r,currentList:s,size:i,containerRef:a}=o,u={overflowX:"auto"},{itemWidth:f,overscan:c=5}=e,d=yt(l,r,f),h=wt(r,f),m=gt("horizontal",c,h,d,o),v=bt(f,r),S=n.computed(()=>v(l.value.start)),p=Rt(f,r);St(i,t,a,m);const w=Et("horizontal",m,v,a),y=n.computed(()=>({style:{height:"100%",width:`${p.value-S.value}px`,marginLeft:`${S.value}px`,display:"flex"}}));return{scrollTo:w,calculateRange:m,wrapperProps:y,containerStyle:u,currentList:s,containerRef:a}}function Fl(e,t){const o=ht(t),{state:l,source:r,currentList:s,size:i,containerRef:a}=o,u={overflowY:"auto"},{itemHeight:f,overscan:c=5}=e,d=yt(l,r,f),h=wt(r,f),m=gt("vertical",c,h,d,o),v=bt(f,r),S=n.computed(()=>v(l.value.start)),p=Rt(f,r);St(i,t,a,m);const w=Et("vertical",m,v,a),y=n.computed(()=>({style:{width:"100%",height:`${p.value-S.value}px`,marginTop:`${S.value}px`}}));return{calculateRange:m,scrollTo:w,containerStyle:u,wrapperProps:y,currentList:s,containerRef:a}}function Pl(e={}){const{navigator:t=z,document:o=j}=e,l=n.shallowRef(!1),r=n.shallowRef(null),s=qe({document:o}),i=W(()=>t&&"wakeLock"in t),a=n.computed(()=>!!r.value&&s.value==="visible");i.value&&(O(r,"release",()=>{var d,h;l.value=(h=(d=r.value)==null?void 0:d.type)!=null?h:!1},{passive:!0}),g.whenever(()=>s.value==="visible"&&o?.visibilityState==="visible"&&l.value,d=>{l.value=!1,u(d)}));async function u(d){var h;await((h=r.value)==null?void 0:h.release()),r.value=i.value?await t.wakeLock.request(d):null}async function f(d){s.value==="visible"?await u(d):l.value=d}async function c(){l.value=!1;const d=r.value;r.value=null,await d?.release()}return{sentinel:r,isSupported:i,isActive:a,request:f,forceRequest:u,release:c}}function Cl(e={}){const{window:t=M,requestPermissions:o=!0}=e,l=e,r=W(()=>{if(!t||!("Notification"in t))return!1;if(Notification.permission==="granted")return!0;try{const y=new Notification("");y.onshow=()=>{y.close()}}catch(y){if(y.name==="TypeError")return!1}return!0}),s=n.shallowRef(r.value&&"permission"in Notification&&Notification.permission==="granted"),i=n.ref(null),a=async()=>{if(r.value)return!s.value&&Notification.permission!=="denied"&&await Notification.requestPermission()==="granted"&&(s.value=!0),s.value},{on:u,trigger:f}=g.createEventHook(),{on:c,trigger:d}=g.createEventHook(),{on:h,trigger:m}=g.createEventHook(),{on:v,trigger:S}=g.createEventHook(),p=async y=>{if(!r.value||!s.value)return;const b=Object.assign({},l,y);return i.value=new Notification(b.title||"",b),i.value.onclick=f,i.value.onshow=d,i.value.onerror=m,i.value.onclose=S,i.value},w=()=>{i.value&&i.value.close(),i.value=null};if(o&&g.tryOnMounted(a),g.tryOnScopeDispose(w),r.value&&t){const y=t.document;O(y,"visibilitychange",b=>{b.preventDefault(),y.visibilityState==="visible"&&w()})}return{isSupported:r,notification:i,ensurePermissions:a,permissionGranted:s,show:p,close:w,onClick:u,onShow:c,onError:h,onClose:v}}const Tt="ping";function Pe(e){return e===!0?{}:e}function Dl(e,t={}){const{onConnected:o,onDisconnected:l,onError:r,onMessage:s,immediate:i=!0,autoConnect:a=!0,autoClose:u=!0,protocols:f=[]}=t,c=n.ref(null),d=n.shallowRef("CLOSED"),h=n.ref(),m=g.toRef(e);let v,S,p=!1,w=0,y=[],b,T;const k=()=>{if(y.length&&h.value&&d.value==="OPEN"){for(const _ of y)h.value.send(_);y=[]}},C=()=>{b!=null&&(clearTimeout(b),b=void 0)},R=()=>{clearTimeout(T),T=void 0},V=(_=1e3,D)=>{C(),!(!g.isClient&&!g.isWorker||!h.value)&&(p=!0,R(),v?.(),h.value.close(_,D),h.value=void 0)},P=(_,D=!0)=>!h.value||d.value!=="OPEN"?(D&&y.push(_),!1):(k(),h.value.send(_),!0),I=()=>{if(p||typeof m.value>"u")return;const _=new WebSocket(m.value,f);h.value=_,d.value="CONNECTING",_.onopen=()=>{d.value="OPEN",w=0,o?.(_),S?.(),k()},_.onclose=D=>{if(d.value="CLOSED",R(),v?.(),l?.(_,D),!p&&t.autoReconnect&&(h.value==null||_===h.value)){const{retries:F=-1,delay:N=1e3,onFailed:H}=Pe(t.autoReconnect);(typeof F=="function"?F:()=>typeof F=="number"&&(F<0||w<F))(w)?(w+=1,b=setTimeout(I,N)):H?.()}},_.onerror=D=>{r?.(_,D)},_.onmessage=D=>{if(t.heartbeat){R();const{message:F=Tt,responseMessage:N=F}=Pe(t.heartbeat);if(D.data===n.toValue(N))return}c.value=D.data,s?.(_,D)}};if(t.heartbeat){const{message:_=Tt,interval:D=1e3,pongTimeout:F=1e3}=Pe(t.heartbeat),{pause:N,resume:H}=g.useIntervalFn(()=>{P(n.toValue(_),!1),T==null&&(T=setTimeout(()=>{V(),p=!1},F))},D,{immediate:!1});v=N,S=H}u&&(g.isClient&&O("beforeunload",()=>V(),{passive:!0}),g.tryOnScopeDispose(V));const L=()=>{!g.isClient&&!g.isWorker||(V(),p=!1,w=0,I())};return i&&L(),a&&n.watch(m,L),{data:c,status:d,close:V,send:P,open:L,ws:h}}function Al(e,t,o){const{window:l=M}=o??{},r=n.ref(null),s=n.shallowRef(),i=(...u)=>{s.value&&s.value.postMessage(...u)},a=function(){s.value&&s.value.terminate()};return l&&(typeof e=="string"?s.value=new Worker(e,t):typeof e=="function"?s.value=e():s.value=e,s.value.onmessage=u=>{r.value=u.data},g.tryOnScopeDispose(()=>{s.value&&s.value.terminate()})),{data:r,post:i,terminate:a,worker:s}}function Ml(e,t){if(e.length===0&&t.length===0)return"";const o=e.map(s=>`'${s}'`).toString(),l=t.filter(s=>typeof s=="function").map(s=>{const i=s.toString();return i.trim().startsWith("function")?i:`const ${s.name} = ${i}`}).join(";"),r=`importScripts(${o});`;return`${o.trim()===""?"":r} ${l}`}function Il(e){return t=>{const o=t.data[0];return Promise.resolve(e.apply(void 0,o)).then(l=>{postMessage(["SUCCESS",l])}).catch(l=>{postMessage(["ERROR",l])})}}function Ll(e,t,o){const l=`${Ml(t,o)}; onmessage=(${Il})(${e})`,r=new Blob([l],{type:"text/javascript"});return URL.createObjectURL(r)}function Nl(e,t={}){const{dependencies:o=[],localDependencies:l=[],timeout:r,window:s=M}=t,i=n.ref(),a=n.shallowRef("PENDING"),u=n.ref({}),f=n.shallowRef(),c=(v="PENDING")=>{i.value&&i.value._url&&s&&(i.value.terminate(),URL.revokeObjectURL(i.value._url),u.value={},i.value=void 0,s.clearTimeout(f.value),a.value=v)};c(),g.tryOnScopeDispose(c);const d=()=>{const v=Ll(e,o,l),S=new Worker(v);return S._url=v,S.onmessage=p=>{const{resolve:w=()=>{},reject:y=()=>{}}=u.value,[b,T]=p.data;switch(b){case"SUCCESS":w(T),c(b);break;default:y(T),c("ERROR");break}},S.onerror=p=>{const{reject:w=()=>{}}=u.value;p.preventDefault(),w(p),c("ERROR")},r&&(f.value=setTimeout(()=>c("TIMEOUT_EXPIRED"),r)),S},h=(...v)=>new Promise((S,p)=>{var w;u.value={resolve:S,reject:p},(w=i.value)==null||w.postMessage([[...v]]),a.value="RUNNING"});return{workerFn:(...v)=>a.value==="RUNNING"?(console.error("[useWebWorkerFn] You can only run one instance of the worker at a time."),Promise.reject()):(i.value=d(),h(...v)),workerStatus:a,workerTerminate:c}}function xl(e={}){const{window:t=M}=e;if(!t)return n.shallowRef(!1);const o=n.shallowRef(t.document.hasFocus()),l={passive:!0};return O(t,"blur",()=>{o.value=!1},l),O(t,"focus",()=>{o.value=!0},l),o}function Wl(e={}){const{window:t=M,...o}=e;return Oe(t,o)}function Hl(e={}){const{window:t=M,initialWidth:o=Number.POSITIVE_INFINITY,initialHeight:l=Number.POSITIVE_INFINITY,listenOrientation:r=!0,includeScrollbar:s=!0,type:i="inner"}=e,a=n.shallowRef(o),u=n.shallowRef(l),f=()=>{if(t)if(i==="outer")a.value=t.outerWidth,u.value=t.outerHeight;else if(i==="visual"&&t.visualViewport){const{width:d,height:h,scale:m}=t.visualViewport;a.value=Math.round(d*m),u.value=Math.round(h*m)}else s?(a.value=t.innerWidth,u.value=t.innerHeight):(a.value=t.document.documentElement.clientWidth,u.value=t.document.documentElement.clientHeight)};f(),g.tryOnMounted(f);const c={passive:!0};if(O("resize",f,c),t&&i==="visual"&&t.visualViewport&&O(t.visualViewport,"resize",f,c),r){const d=$("(orientation: portrait)");n.watch(d,()=>f())}return{width:a,height:u}}E.DefaultMagicKeysAliasMap=Ze,E.StorageSerializers=Re,E.TransitionPresets=gl,E.asyncComputed=Ce,E.breakpointsAntDesign=on,E.breakpointsBootstrapV5=en,E.breakpointsElement=un,E.breakpointsMasterCss=rn,E.breakpointsPrimeFlex=sn,E.breakpointsQuasar=ln,E.breakpointsSematic=an,E.breakpointsTailwind=Zt,E.breakpointsVuetify=nn,E.breakpointsVuetifyV2=Le,E.breakpointsVuetifyV3=tn,E.cloneFnJSON=ne,E.computedAsync=Ce,E.computedInject=kt,E.createFetch=zn,E.createReusableTemplate=_t,E.createTemplatePromise=Ft,E.createUnrefFn=Pt,E.customStorageEventName=Ee,E.defaultDocument=j,E.defaultLocation=Ct,E.defaultNavigator=z,E.defaultWindow=M,E.executeTransition=vt,E.formatTimeAgo=dt,E.getSSRHandler=fe,E.mapGamepadToXbox360Controller=lo,E.onClickOutside=Dt,E.onElementRemoval=we,E.onKeyDown=Mt,E.onKeyPressed=It,E.onKeyStroke=ie,E.onKeyUp=Lt,E.onLongPress=Wt,E.onStartTyping=$t,E.provideSSRWidth=Qt,E.setSSRHandler=wn,E.templateRef=Bt,E.unrefElement=x,E.useActiveElement=Me,E.useAnimate=jt,E.useAsyncQueue=zt,E.useAsyncState=Ie,E.useBase64=Yt,E.useBattery=Kt,E.useBluetooth=Jt,E.useBreakpoints=cn,E.useBroadcastChannel=fn,E.useBrowserLocation=dn,E.useCached=mn,E.useClipboard=vn,E.useClipboardItems=pn,E.useCloned=hn,E.useColorMode=Ue,E.useConfirmDialog=bn,E.useCountdown=Sn,E.useCssVar=oe,E.useCurrentElement=$e,E.useCycleList=Rn,E.useDark=En,E.useDebouncedRefHistory=_n,E.useDeviceMotion=Vn,E.useDeviceOrientation=ze,E.useDevicePixelRatio=Fn,E.useDevicesList=Pn,E.useDisplayMedia=Cn,E.useDocumentVisibility=qe,E.useDraggable=Dn,E.useDropZone=An,E.useElementBounding=Mn,E.useElementByPoint=In,E.useElementHover=Ln,E.useElementSize=Ge,E.useElementVisibility=Xe,E.useEventBus=Nn,E.useEventListener=O,E.useEventSource=Wn,E.useEyeDropper=Hn,E.useFavicon=Un,E.useFetch=Ke,E.useFileDialog=Xn,E.useFileSystemAccess=Kn,E.useFocus=Jn,E.useFocusWithin=to,E.useFps=no,E.useFullscreen=oo,E.useGamepad=ao,E.useGeolocation=ro,E.useIdle=uo,E.useImage=fo,E.useInfiniteScroll=mo,E.useIntersectionObserver=Ye,E.useKeyModifier=po,E.useLocalStorage=ho,E.useMagicKeys=yo,E.useManualRefHistory=je,E.useMediaControls=bo,E.useMediaQuery=$,E.useMemoize=So,E.useMemory=Ro,E.useMounted=Ae,E.useMouse=et,E.useMouseInElement=tt,E.useMousePressed=To,E.useMutationObserver=Q,E.useNavigatorLanguage=Oo,E.useNetwork=nt,E.useNow=ot,E.useObjectUrl=ko,E.useOffsetPagination=_o,E.useOnline=Vo,E.usePageLeave=Fo,E.useParallax=Po,E.useParentElement=Co,E.usePerformanceObserver=Do,E.usePermission=se,E.usePointer=Mo,E.usePointerLock=Io,E.usePointerSwipe=Lo,E.usePreferredColorScheme=No,E.usePreferredContrast=xo,E.usePreferredDark=We,E.usePreferredLanguages=Wo,E.usePreferredReducedMotion=Ho,E.usePreferredReducedTransparency=Uo,E.usePrevious=$o,E.useRafFn=Y,E.useRefHistory=Te,E.useResizeObserver=me,E.useSSRWidth=Se,E.useScreenOrientation=at,E.useScreenSafeArea=Bo,E.useScriptTag=jo,E.useScroll=Oe,E.useScrollLock=qo,E.useSessionStorage=Go,E.useShare=Yo,E.useSorted=Ko,E.useSpeechRecognition=Jo,E.useSpeechSynthesis=Qo,E.useStepper=Zo,E.useStorage=de,E.useStorageAsync=el,E.useStyleTag=nl,E.useSupported=W,E.useSwipe=ol,E.useTemplateRefsList=ll,E.useTextDirection=al,E.useTextSelection=il,E.useTextareaAutosize=ul,E.useThrottledRefHistory=cl,E.useTimeAgo=vl,E.useTimeoutPoll=pl,E.useTimestamp=hl,E.useTitle=yl,E.useTransition=Sl,E.useUrlSearchParams=Rl,E.useUserMedia=El,E.useVModel=pt,E.useVModels=Tl,E.useVibrate=Ol,E.useVirtualList=kl,E.useWakeLock=Pl,E.useWebNotification=Cl,E.useWebSocket=Dl,E.useWebWorker=Al,E.useWebWorkerFn=Nl,E.useWindowFocus=xl,E.useWindowScroll=Wl,E.useWindowSize=Hl,Object.keys(g).forEach(function(e){e!=="default"&&!Object.prototype.hasOwnProperty.call(E,e)&&Object.defineProperty(E,e,{enumerable:!0,get:function(){return g[e]}})})})(this.VueUse=this.VueUse||{},VueUse,Vue);
